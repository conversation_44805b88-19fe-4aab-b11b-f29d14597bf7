# HopCop 替换系统详解

## 概述

替换(Substitution)系统是 HopCop 定理证明器的核心组件之一，负责处理变量的统一、绑定和解引用。本文档通过具体例子详细说明替换系统的工作原理。

## 核心概念

### 1. 替换(Substitution)
- **定义**: 将变量映射到项的函数
- **表示**: `{X0/l1 -> a/l0, X1/l2 -> f(X0)/l1}`
- **作用**: 使不同的项能够统一(变得相等)

### 2. 统一(Unification)
- **定义**: 使两个项相等的过程
- **算法**: Robinson统一算法的变种
- **结果**: 最一般统一项(Most General Unifier, MGU)

### 3. 位置感知(Location-Aware)
- **定义**: 考虑项在连接表中的位置
- **格式**: `项/位置`，如 `X0/l1` 表示位置 l1 的变量 X0
- **基项优化**: 基项(ground terms)总是位于根位置 l0

### 4. 出现检查(Occurs Check)
- **定义**: 防止变量绑定到包含自身的项
- **例子**: 阻止 `X0 = f(X0)` 这样的无限循环绑定

## 实际例子演示

### 例子1: 基础变量绑定

```
初始状态: {}
操作: 统一 X0/l1 和 a/l0
结果: {X0/l1 -> a/l0}
```

**解释**: 变量 X0 在位置 l1 被绑定到常量 a(位于根位置 l0)

### 例子2: 函数项统一

```
操作: 统一 f(X0)/l1 和 f(a)/l2
过程:
1. 比较函数符号: f = f ✓
2. 统一参数: X0/l1 和 a/l0
结果: {X0/l1 -> a/l0}
统一后: f(X0)/l1 → f(a), f(a)/l2 → f(a)
```

### 例子3: 复杂项统一

```
操作: 统一 g(X0, f(X1))/l1 和 g(a, f(a))/l2
过程:
1. 比较函数符号: g = g ✓
2. 统一第一个参数: X0/l1 和 a/l0 → {X0/l1 -> a/l0}
3. 统一第二个参数: f(X1)/l1 和 f(a)/l0
   - 比较函数符号: f = f ✓
   - 统一参数: X1/l1 和 a/l0 → {X1/l1 -> a/l0}
结果: {X0/l1 -> a/l0, X1/l1 -> a/l0}
```

### 例子4: 出现检查

```
操作: 尝试统一 X0/l1 和 f(X0)/l1
检查: X0 出现在 f(X0) 中
结果: 失败 - 会创建无限循环 X0 = f(X0) = f(f(X0)) = ...
```

### 例子5: 链式绑定和查找

```
步骤1: X0/l1 -> X1/l1
步骤2: X1/l1 -> a/l0
最终: {X0/l1 -> X1/l1, X1/l1 -> a/l0}

查找 X0/l1:
1. X0/l1 绑定到 X1/l1
2. X1/l1 绑定到 a/l0
3. 最终结果: a/l0
```

## 关键算法

### 统一算法流程

```
function unify(left, right):
    while 有待处理的项对:
        left = lookup(left)   // 解引用
        right = lookup(right) // 解引用
        
        if left == right:
            continue  // 已经相等
            
        match (left, right):
            (Var(x), term) -> bind(x, term)
            (term, Var(x)) -> bind(x, term)
            (App(f, args1), App(g, args2)):
                if f != g: return false
                add (args1[i], args2[i]) to queue
    return true
```

### 查找算法

```
function lookup(term):
    while term is Var(x) and x is bound:
        term = binding[x]
    return term
```

## 测试用例说明

### 运行测试

```bash
# 基础替换操作
cargo test test_substitution_basics -- --nocapture

# 统一过程演示
cargo test test_unification_process -- --nocapture

# 复杂统一示例
cargo test test_complex_unification -- --nocapture

# 出现检查测试
cargo test test_occurs_check -- --nocapture

# 替换查找演示
cargo test test_substitution_lookup -- --nocapture

# 文字统一测试
cargo test test_literal_unification -- --nocapture

# 等式处理测试
cargo test test_equality_handling -- --nocapture

# 完整工作流程
cargo test test_substitution_workflow -- --nocapture

# 运行所有替换测试
cargo test subst::tests -- --nocapture
```

## 实际输出示例

### 基础绑定
```
初始替换: {}
添加绑定 X0/l1 -> a/l0
替换结果: {0/l1 -> a/l0}
```

### 复杂统一
```
左项: g(X0, f(X1))/l1
右项: g(a, f(a))/l2
统一成功: true
替换结果: {1/l1 -> a/l0, 0/l1 -> a/l0}
统一后的左项: g(a,f(a))
统一后的右项: g(a,f(a))
```

### 出现检查
```
尝试统一 X0/l1 和 f(X0)/l1
统一成功: false
✓ 出现检查正确阻止了无限循环
```

## 在证明搜索中的作用

1. **文字统一**: 在扩展和归约规则中统一互补文字
2. **变量绑定**: 记录推理过程中的变量约束
3. **一致性检查**: 确保绑定不会导致矛盾
4. **回溯支持**: 支持搜索过程中的状态恢复

## 性能优化

1. **位置感知**: 基项总是在根位置，减少不必要的位置信息
2. **增量操作**: 支持截断操作，便于回溯
3. **出现检查**: 防止无限循环，保证算法终止
4. **索引映射**: 使用 IndexMap 保持插入顺序

替换系统是连接表证明方法的基础，它确保了推理过程的正确性和完整性。通过这些测试用例，我们可以清楚地看到替换系统如何处理各种复杂的统一场景。
