# HopCop 搜索过程详解

## 概述

HopCop 是一个基于连接表(Tableau)方法的自动定理证明器。本文档通过具体例子详细说明其搜索过程的工作原理。

## 核心概念

### 1. 连接表(Tableau)
- **定义**: 树状结构，每个节点包含一个子句
- **作用**: 构建证明树，通过系统地扩展和关闭分支来寻找矛盾

### 2. 开放分支(Open Branch)
- **定义**: 需要被关闭的文字
- **标识**: `位置_索引` 格式，如 `l0_0` 表示位置 l0 的第 0 个文字

### 3. 三种推理规则

#### 开始规则(Start Rule)
- 选择一个起始子句(通常是目标的否定)
- 将其添加到连接表的根节点
- 为子句中的每个文字创建开放分支

#### 扩展规则(Extension Rule)
- 选择一个开放分支
- 寻找包含互补文字的子句
- 将该子句添加到连接表中
- 关闭当前分支，创建新的开放分支

#### 归约规则(Reduction Rule)
- 选择一个开放分支
- 在其祖先路径上寻找互补文字
- 如果找到，则关闭该分支

## 实际例子演示

### 问题设置
证明以下公理集合是不一致的：
```
1. P(a) ∨ Q(a)     (要么P(a)为真，要么Q(a)为真)
2. ¬P(a) ∨ R(a)    (如果P(a)为真，则R(a)为真)
3. ¬Q(a) ∨ R(a)    (如果Q(a)为真，则R(a)为真)
4. ¬R(a)           (R(a)为假，这是目标的否定)
```

### 搜索步骤详解

#### 步骤1 - 开始规则
```
操作: 选择子句4 (¬R(a)) 作为起始点
结果: 
- 连接表: l0 ← ¬R(a)
- 开放分支: [l0_0]
- 踪迹: [~R(a)@l0_0]
```

#### 步骤2 - 扩展规则
```
操作: 关闭分支 l0_0 (¬R(a))
选择: 子句2 (¬Q(a) ∨ R(a))，使用 R(a) 与 ¬R(a) 统一
结果:
- 连接表: l0 ← ¬R(a), l1 ← (¬Q(a) ∨ R(a))
- 开放分支: [l1_0] (¬Q(a))
- 关闭分支: l0_0 (通过与 l1_1 统一)
```

#### 步骤3 - 继续扩展
```
操作: 关闭分支 l1_0 (¬Q(a))
选择: 子句0 (P(a) ∨ Q(a))，使用 Q(a) 与 ¬Q(a) 统一
结果:
- 连接表: l0 ← ¬R(a), l1 ← (¬Q(a) ∨ R(a)), l2 ← (P(a) ∨ Q(a))
- 开放分支: [l2_0] (P(a))
- 关闭分支: l1_0 (通过与 l2_1 统一)
```

#### 步骤4 - 最后扩展
```
操作: 关闭分支 l2_0 (P(a))
选择: 子句1 (¬P(a) ∨ R(a))，使用 ¬P(a) 与 P(a) 统一
结果:
- 连接表: l0 ← ¬R(a), l1 ← (¬Q(a) ∨ R(a)), l2 ← (P(a) ∨ Q(a)), l3 ← (¬P(a) ∨ R(a))
- 开放分支: [l3_1] (R(a))
- 关闭分支: l2_0 (通过与 l3_0 统一)
```

#### 步骤5 - 归约规则
```
操作: 关闭分支 l3_1 (R(a))
发现: 可以与祖先节点 l0_0 的 ¬R(a) 进行归约
结果:
- 所有分支都已关闭
- 证明完成！矛盾被发现
```

## 关键数据结构

### 踪迹(Trail)
记录搜索过程中的所有约束和绑定：
- `文字@分支`: 文字在特定分支的位置
- `分支1≁分支2`: 不能归约关系
- `变量->项`: 变量绑定

### 替换(Substitution)
- 管理变量的统一和绑定
- 确保逻辑一致性

### 学习机制
- 当搜索失败时，学习导致失败的原因
- 避免重复相同的错误路径
- 实现冲突驱动的回溯

## 符号说明

- `l0, l1, l2, l3`: 连接表中的位置标识
- `l0_0, l1_0, l2_0`: 分支标识 (位置_文字索引)
- `@`: 文字在特定分支的位置
- `≁`: 不能归约关系
- `~`: 否定符号
- `∨`: 析取(或)
- `∧`: 合取(和)

## 运行测试

要观察搜索过程的实际工作，可以运行以下测试：

```bash
# 基本搜索初始化测试
cargo test test_search_initialization -- --nocapture

# 简单搜索步骤演示
cargo test test_search_step_by_step -- --nocapture

# 复杂搜索演示
cargo test test_complex_search -- --nocapture

# 详细解释
cargo test test_search_explanation -- --nocapture

# 运行所有搜索测试
cargo test search::tests -- --nocapture
```

这些测试展示了搜索算法的每个步骤，包括连接表的构建、分支的开放和关闭、以及最终证明的发现过程。
