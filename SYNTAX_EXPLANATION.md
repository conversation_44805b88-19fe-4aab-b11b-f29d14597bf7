# HopCop 语法结构详解

## 概述

HopCop 的语法模块定义了逻辑公式的内部表示结构。本文档通过具体例子详细说明各种语法结构的工作原理和层次关系。

## 核心数据结构

### 1. 名称(Name)
符号的标识符，支持多种类型：

```rust
enum Name {
    Equality,                    // 等式符号 "sPE"
    Atom(&'static str),         // 普通原子 "P", "f", "a"
    Quoted(&'static str),       // 引用名称 "'quoted_name'"
    Number(&'static str),       // 数字 "42"
    Distinct(&'static str),     // 区别对象 "\"distinct\""
    Skolem(usize),             // Skolem函数 "sK1"
    Definition(usize),         // 定义谓词 "sP2"
}
```

**显示示例**:
- `Equality` → `sPE`
- `Atom("P")` → `P`
- `Quoted("name")` → `'name'`
- `Skolem(1)` → `sK1`

### 2. 符号(Symbol)
带有元数和类型信息的符号：

```rust
struct Symbol {
    arity: usize,    // 元数(参数个数)
    sort: Sort,      // 类型(Bool/Obj)
    name: Name,      // 符号名称
}
```

**示例**:
- 常量: `Symbol { arity: 0, sort: Obj, name: Atom("a") }`
- 函数: `Symbol { arity: 2, sort: Obj, name: Atom("f") }`
- 谓词: `Symbol { arity: 1, sort: Bool, name: Atom("P") }`
- 等式: `Symbol { arity: 2, sort: Bool, name: Equality }`

### 3. 应用(Application)
符号应用到参数的结构：

```rust
struct Application {
    symbol: Perfect<Symbol>,  // 符号
    args: Box<[Term]>,       // 参数列表
    ground: bool,            // 是否为基项(不含变量)
}
```

**示例**:
- 常量: `a` (无参数)
- 函数应用: `f(a, X0)` (两个参数)
- 谓词应用: `P(a)` (一个参数)

### 4. 项(Term)
变量或应用的统一表示：

```rust
enum Term {
    Var(usize),                    // 变量 X0, X1, ...
    App(Perfect<Application>),     // 应用
}
```

**示例**:
- 变量: `X0`, `X1`
- 常量: `a`
- 复合项: `f(a, X0)`

### 5. 文字(Literal)
带极性的原子公式：

```rust
struct Literal {
    polarity: bool,                // 极性(true=正, false=负)
    atom: Perfect<Application>,    // 原子公式
}
```

**显示规则**:
- 正文字: `P(a)`
- 负文字: `~P(a)`
- 等式: `X0 = a`
- 不等式: `X0 != a`

### 6. 子句(Clause)
文字的析取加上元信息：

```rust
struct Clause {
    literals: Vec<Literal>,        // 文字列表
    disequations: Vec<Disequation>, // 不等式约束
    info: Info,                    // 元信息
}
```

**TPTP格式显示**: `cnf(1, axiom, P(a) | ~Q(a)). % X0 ≠ X1`

## 特殊结构

### 引用计数版本(Rc Structures)
为了内存效率和共享，提供了引用计数版本：

- `RcTerm`: 使用 `Rc<RcApplication>` 的项
- `RcApplication`: 使用引用计数的应用
- `RcLiteral`: 使用引用计数的文字

### 公式结构(Formula)
一般的逻辑公式表示：

```rust
enum Formula {
    Bool(bool),                           // 布尔常量
    Atom(Rc<RcApplication>),             // 原子公式
    Not(Box<Formula>),                   // 否定
    And(Vec<Formula>),                   // 合取
    Or(Vec<Formula>),                    // 析取
    Iff(Box<Formula>, Box<Formula>),     // 等价
    Forall(usize, Box<Formula>),         // 全称量化
    Exists(usize, Box<Formula>),         // 存在量化
}
```

### 否定范式(NNF)
否定下推的范式：

```rust
enum Nnf {
    Literal(RcLiteral),          // 文字
    And(Vec<Nnf>),              // 合取
    Or(Vec<Nnf>),               // 析取
    Forall(usize, Box<Nnf>),    // 全称量化
    Exists(usize, Box<Nnf>),    // 存在量化
}
```

## 实际例子演示

### 例子1: 构建简单谓词

```rust
// 1. 创建符号
let p_symbol = Symbol {
    arity: 1,
    sort: Sort::Bool,
    name: Name::Atom("P"),
};

// 2. 创建常量
let a_symbol = Symbol {
    arity: 0,
    sort: Sort::Obj,
    name: Name::Atom("a"),
};

// 3. 构建应用 P(a)
let a_app = Application {
    symbol: Perfect::new(a_symbol),
    args: vec![].into(),
    ground: true,
};

let pa_app = Application {
    symbol: Perfect::new(p_symbol),
    args: vec![Term::App(Perfect::new(a_app))].into(),
    ground: true,
};

// 4. 创建文字
let literal = Literal {
    polarity: true,
    atom: Perfect::new(pa_app),
};
// 显示: P(a)
```

### 例子2: 构建复合项

```rust
// f(a, X0)
let f_symbol = Symbol {
    arity: 2,
    sort: Sort::Obj,
    name: Name::Atom("f"),
};

let fax0_app = Application {
    symbol: Perfect::new(f_symbol),
    args: vec![
        Term::App(Perfect::new(a_app)),  // 常量 a
        Term::Var(0),                    // 变量 X0
    ].into(),
    ground: false,  // 包含变量，不是基项
};
// 显示: f(a,X0)
```

### 例子3: 构建等式

```rust
// X0 = a
let eq_symbol = Symbol {
    arity: 2,
    sort: Sort::Bool,
    name: Name::Equality,
};

let eq_app = Application {
    symbol: Perfect::new(eq_symbol),
    args: vec![
        Term::Var(0),                    // X0
        Term::App(Perfect::new(a_app)),  // a
    ].into(),
    ground: false,
};

let eq_literal = Literal {
    polarity: true,
    atom: Perfect::new(eq_app),
};
// 显示: X0 = a

let neq_literal = Literal {
    polarity: false,
    atom: Perfect::new(eq_app),
};
// 显示: X0 != a
```

## 工作流程

### 1. 自底向上构建
```
Name → Symbol → Application → Term → Literal → Clause → Matrix
```

### 2. 类型检查
- `Sort::Obj`: 对象类型(常量、函数)
- `Sort::Bool`: 布尔类型(谓词、等式)

### 3. 基项检查
- 基项: 不包含变量的项
- 非基项: 包含变量的项

### 4. 显示格式
- 普通谓词: `P(a)`
- 否定谓词: `~P(a)`
- 等式: `X0 = a`
- 不等式: `X0 != a`
- 子句: `cnf(1, axiom, P(a) | ~Q(a)).`

## 测试用例

### 运行测试

```bash
# 名称显示测试
cargo test test_name_display -- --nocapture

# 符号创建测试
cargo test test_symbol_creation -- --nocapture

# 项构造测试
cargo test test_term_construction -- --nocapture

# 文字构造测试
cargo test test_literal_construction -- --nocapture

# 子句构造测试
cargo test test_clause_construction -- --nocapture

# 完整工作流程
cargo test test_syntax_workflow -- --nocapture

# 运行所有语法测试
cargo test syntax::tests -- --nocapture
```

## 在证明搜索中的作用

1. **表示逻辑公式**: 将输入的逻辑公式转换为内部表示
2. **支持统一**: 提供统一算法所需的结构化数据
3. **优化存储**: 使用 Perfect 指针和引用计数优化内存使用
4. **类型安全**: 通过 Rust 类型系统确保结构正确性
5. **显示输出**: 支持多种格式的公式显示

语法模块是整个证明器的基础，它定义了所有逻辑结构的内部表示，为后续的解析、转换、搜索等操作提供了坚实的基础。
