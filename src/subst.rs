use fnv::FnvBuildHasher;
use indexmap::IndexMap;
use std::fmt;

use crate::syntax::{Application, IsGround, Literal, Term};
use crate::util::Perfect;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub(crate) struct Location(usize);

impl Location {
    pub(crate) fn new(index: usize) -> Self {
        Self(index)
    }
}

pub(crate) const ROOT: Location = Location(0);

impl fmt::Display for Location {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "l{}", self.0)
    }
}

#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub(crate) struct Branch {
    pub(crate) location: Location,
    pub(crate) index: usize,
}

impl fmt::Display for Branch {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}_{}", self.location, self.index)
    }
}

#[derive(Debug, <PERSON>lone, Co<PERSON>, PartialEq, Eq, Hash)]
pub(crate) struct Located<T> {
    location: Location,
    item: T,
}

impl Location {
    pub(crate) fn locate<T: IsGround>(self, item: T) -> Located<T> {
        let location = if item.is_ground() { ROOT } else { self };
        Located { location, item }
    }
}

impl<T: fmt::Display> fmt::Display for Located<T> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}/{}", self.item, self.location)
    }
}

impl<T> Located<T> {
    fn transfer<S: IsGround>(&self, item: S) -> Located<S> {
        self.location.locate(item)
    }

    pub(crate) fn map<S, F: FnOnce(T) -> S>(self, f: F) -> Located<S> {
        Located {
            location: self.location,
            item: f(self.item),
        }
    }
}

#[derive(Default, Debug)]
pub(crate) struct Substitution {
    map: IndexMap<Located<usize>, Located<Term>, FnvBuildHasher>,
    unify: Vec<(Located<Term>, Located<Term>)>,
    occurs: Vec<Located<Term>>,
}

impl fmt::Display for Substitution {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{{")?;
        let mut first = true;
        for (x, t) in &self.map {
            if !first {
                write!(f, ", ")?;
            }
            write!(f, "{x} -> {t}")?;
            first = false;
        }
        write!(f, "}}")
    }
}

impl Substitution {
    pub(crate) fn is_empty(&self) -> bool {
        self.len() == 0
    }

    pub(crate) fn len(&self) -> usize {
        self.map.len()
    }

    pub(crate) fn clear(&mut self) {
        self.map.clear();
    }

    pub(crate) fn truncate(&mut self, to: usize) {
        self.map.truncate(to);
    }

    pub(crate) fn unify(&mut self, left: Located<Term>, right: Located<Term>) -> bool {
        let start = self.map.len();
        self.unify.clear();
        let mut next = Some((left, right));
        while let Some((left, right)) = next {
            let left = self.lookup(left);
            let right = self.lookup(right);
            if left == right {
                next = self.unify.pop();
                continue;
            }
            match (left.item, right.item) {
                (Term::Var(x), Term::Var(_)) => {
                    let left = left.transfer(x);
                    self.map.insert(left, right);
                }
                (Term::Var(x), Term::App(app)) => {
                    if !self.bind_unchecked(left.transfer(x), right.transfer(app)) {
                        self.truncate(start);
                        return false;
                    }
                }
                (Term::App(t), Term::Var(x)) => {
                    if !self.bind_unchecked(right.transfer(x), left.transfer(t)) {
                        self.truncate(start);
                        return false;
                    }
                }
                (Term::App(lapp), Term::App(rapp)) => {
                    if lapp.symbol != rapp.symbol {
                        self.truncate(start);
                        return false;
                    }
                    self.unify.extend(Iterator::zip(
                        lapp.args.iter().map(|arg| left.transfer(*arg)),
                        rapp.args.iter().map(|arg| right.transfer(*arg)),
                    ));
                }
            }
            next = self.unify.pop();
        }
        true
    }

    pub(crate) fn connect(&mut self, l: Located<Literal>, k: Located<Literal>) -> bool {
        self.unify(l.map(|l| Term::App(l.atom)), k.map(|r| Term::App(r.atom)))
    }

    pub(crate) fn equal(&mut self, left: Located<Term>, right: Located<Term>) -> bool {
        self.unify.clear();
        let mut next = Some((left, right));
        while let Some((left, right)) = next {
            let left = self.lookup(left);
            let right = self.lookup(right);
            if left == right {
                next = self.unify.pop();
                continue;
            }
            if let (Term::App(lapp), Term::App(rapp)) = (left.item, right.item) {
                if lapp.symbol != rapp.symbol {
                    return false;
                }
                self.unify.extend(Iterator::zip(
                    lapp.args.iter().map(|arg| left.transfer(*arg)),
                    rapp.args.iter().map(|arg| right.transfer(*arg)),
                ));
            } else {
                return false;
            }
            next = self.unify.pop();
        }
        true
    }

    fn bind_unchecked(&mut self, x: Located<usize>, t: Located<Perfect<Application>>) -> bool {
        self.occurs.clear();
        self.occurs
            .extend(t.item.args.iter().map(|arg| t.transfer(*arg)));
        while let Some(next) = self.occurs.pop() {
            let next = self.lookup(next);
            match next.item {
                Term::Var(y) => {
                    if x == next.transfer(y) {
                        return false;
                    }
                }
                Term::App(app) => {
                    self.occurs
                        .extend(app.args.iter().map(|arg| next.transfer(*arg)));
                }
            }
        }
        self.map.insert(x, t.map(Term::App));
        true
    }

    fn lookup(&self, mut term: Located<Term>) -> Located<Term> {
        while let Term::Var(x) = term.item {
            if let Some(bound) = self.map.get(&term.transfer(x)) {
                term = *bound;
            } else {
                break;
            }
        }
        term
    }

    pub(crate) fn bindings(&self) -> impl Iterator<Item = (&Located<usize>, &Located<Term>)> {
        self.bindings_since(0)
    }

    pub(crate) fn bindings_since(
        &self,
        since: usize,
    ) -> impl Iterator<Item = (&Located<usize>, &Located<Term>)> {
        self.map[since..].iter()
    }
}

pub(crate) struct Substituted<'a, T> {
    pub(crate) substitution: &'a Substitution,
    pub(crate) item: T,
}

impl<'a> fmt::Display for Substituted<'a, Located<Perfect<Application>>> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let located = &self.item;
        let app = located.item;
        write!(f, "{}", app.symbol.name)?;
        if !app.args.is_empty() {
            write!(f, "(")?;
            let mut first = true;
            for arg in &app.args {
                if !first {
                    write!(f, ",")?;
                }
                first = false;
                Substituted {
                    substitution: self.substitution,
                    item: located.transfer(*arg),
                }
                .fmt(f)?;
            }
            write!(f, ")")?;
        }
        Ok(())
    }
}

impl<'a> fmt::Display for Substituted<'a, Located<Term>> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let lookup = self.substitution.lookup(self.item);
        match lookup.item {
            Term::Var(x) => write!(f, "X{}_{}", x, lookup.location.0),
            Term::App(app) => Substituted {
                substitution: self.substitution,
                item: lookup.transfer(app),
            }
            .fmt(f),
        }
    }
}

impl<'a> fmt::Display for Substituted<'a, Located<Literal>> {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        let located = &self.item;
        let literal = located.item;
        if literal.atom.symbol.is_equality() {
            Substituted {
                substitution: self.substitution,
                item: located.transfer(literal.atom.args[0]),
            }
            .fmt(f)?;
            write!(f, "{}", if literal.polarity { " = " } else { " != " })?;
            Substituted {
                substitution: self.substitution,
                item: located.transfer(literal.atom.args[1]),
            }
            .fmt(f)?;
        } else {
            if !literal.polarity {
                write!(f, "~")?;
            }
            Substituted {
                substitution: self.substitution,
                item: located.transfer(literal.atom),
            }
            .fmt(f)?;
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::syntax::*;
    use crate::util::Perfect;

    // 创建测试用的符号和应用
    fn create_test_symbols() -> (Perfect<Symbol>, Perfect<Symbol>, Perfect<Symbol>) {
        // 创建函数符号 f(x)
        let f_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Obj,
            name: Name::Atom("f"),
        });

        // 创建函数符号 g(x, y)
        let g_symbol = Perfect::new(Symbol {
            arity: 2,
            sort: Sort::Obj,
            name: Name::Atom("g"),
        });

        // 创建常量符号 a
        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        (f_symbol, g_symbol, a_symbol)
    }

    fn create_test_applications(
        f_symbol: Perfect<Symbol>,
        g_symbol: Perfect<Symbol>,
        a_symbol: Perfect<Symbol>,
    ) -> (
        Perfect<Application>,
        Perfect<Application>,
        Perfect<Application>,
    ) {
        // 常量 a
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        // f(X0) - 包含变量的应用
        let fx_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::Var(0)].into(),
            ground: false,
        });

        // g(X0, a) - 混合变量和常量的应用
        let gxa_app = Perfect::new(Application {
            symbol: g_symbol,
            args: vec![Term::Var(0), Term::App(a_app)].into(),
            ground: false,
        });

        (a_app, fx_app, gxa_app)
    }

    #[test]
    fn test_substitution_basics() {
        println!("=== 测试替换基础操作 ===");

        let mut subst = Substitution::default();
        let (_, _, a_symbol) = create_test_symbols();
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        println!("初始替换: {}", subst);
        println!("替换为空: {}", subst.is_empty());
        println!("替换大小: {}", subst.len());

        // 创建位置定位的项
        let loc1 = Location::new(1);

        // X0/l1 -> a/l0 (变量 X0 在位置 l1 绑定到常量 a)
        let x0_l1 = loc1.locate(Term::Var(0));
        let a_l0 = ROOT.locate(Term::App(a_app));

        println!("\n--- 添加绑定 X0/l1 -> a/l0 ---");
        let success = subst.unify(x0_l1, a_l0);
        println!("统一成功: {}", success);
        println!("替换结果: {}", subst);
        println!("替换大小: {}", subst.len());

        // 显示绑定
        println!("所有绑定:");
        for (var, term) in subst.bindings() {
            println!("  {} -> {}", var, term);
        }
    }

    #[test]
    fn test_unification_process() {
        println!("\n=== 测试统一过程 ===");

        let mut subst = Substitution::default();
        let (f_symbol, g_symbol, a_symbol) = create_test_symbols();
        let (a_app, fx_app, gxa_app) = create_test_applications(f_symbol, g_symbol, a_symbol);

        let loc1 = Location::new(1);
        let loc2 = Location::new(2);

        // 测试1: 统一 f(X0) 和 f(a)
        println!("\n--- 测试1: 统一 f(X0) 和 f(a) ---");

        // 创建 f(a)
        let fa_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        let fx_l1 = loc1.locate(Term::App(fx_app));
        let fa_l2 = loc2.locate(Term::App(fa_app));

        println!("左项: f(X0)/l1");
        println!("右项: f(a)/l2");

        let success = subst.unify(fx_l1, fa_l2);
        println!("统一成功: {}", success);
        println!("替换结果: {}", subst);

        // 显示统一后的项
        println!(
            "统一后的左项: {}",
            Substituted {
                substitution: &subst,
                item: fx_l1
            }
        );
        println!(
            "统一后的右项: {}",
            Substituted {
                substitution: &subst,
                item: fa_l2
            }
        );
    }

    #[test]
    fn test_complex_unification() {
        println!("\n=== 测试复杂统一 ===");

        let mut subst = Substitution::default();
        let (f_symbol, g_symbol, a_symbol) = create_test_symbols();

        let loc1 = Location::new(1);
        let loc2 = Location::new(2);

        // 创建复杂的项: g(X0, f(X1)) 和 g(a, f(a))
        let fx1_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::Var(1)].into(),
            ground: false,
        });

        let fa_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::App(Perfect::new(Application {
                symbol: a_symbol,
                args: vec![].into(),
                ground: true,
            }))]
            .into(),
            ground: true,
        });

        let gx0fx1_app = Perfect::new(Application {
            symbol: g_symbol,
            args: vec![Term::Var(0), Term::App(fx1_app)].into(),
            ground: false,
        });

        let gafa_app = Perfect::new(Application {
            symbol: g_symbol,
            args: vec![
                Term::App(Perfect::new(Application {
                    symbol: a_symbol,
                    args: vec![].into(),
                    ground: true,
                })),
                Term::App(fa_app),
            ]
            .into(),
            ground: true,
        });

        let left = loc1.locate(Term::App(gx0fx1_app));
        let right = loc2.locate(Term::App(gafa_app));

        println!("左项: g(X0, f(X1))/l1");
        println!("右项: g(a, f(a))/l2");

        let success = subst.unify(left, right);
        println!("统一成功: {}", success);
        println!("替换结果: {}", subst);

        println!("所有绑定:");
        for (var, term) in subst.bindings() {
            println!("  {} -> {}", var, term);
        }

        // 显示统一后的项
        println!(
            "统一后的左项: {}",
            Substituted {
                substitution: &subst,
                item: left
            }
        );
        println!(
            "统一后的右项: {}",
            Substituted {
                substitution: &subst,
                item: right
            }
        );
    }

    #[test]
    fn test_occurs_check() {
        println!("\n=== 测试出现检查(Occurs Check) ===");

        let mut subst = Substitution::default();
        let (f_symbol, _, _) = create_test_symbols();

        let loc1 = Location::new(1);

        // 尝试统一 X0 和 f(X0) - 这应该失败因为出现检查
        let x0 = loc1.locate(Term::Var(0));

        let fx0_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::Var(0)].into(),
            ground: false,
        });
        let fx0 = loc1.locate(Term::App(fx0_app));

        println!("尝试统一 X0/l1 和 f(X0)/l1");
        println!("这应该失败因为会创建无限循环: X0 = f(X0) = f(f(X0)) = ...");

        let success = subst.unify(x0, fx0);
        println!("统一成功: {}", success);
        println!("替换结果: {}", subst);

        if !success {
            println!("✓ 出现检查正确阻止了无限循环");
        }
    }

    #[test]
    fn test_substitution_lookup() {
        println!("\n=== 测试替换查找 ===");

        let mut subst = Substitution::default();
        let (f_symbol, _, a_symbol) = create_test_symbols();

        let loc1 = Location::new(1);
        let loc2 = Location::new(2);

        // 创建链式绑定: X0 -> X1 -> a
        let x0_l1 = loc1.locate(Term::Var(0));
        let x1_l1 = loc1.locate(Term::Var(1));
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });
        let a_l0 = ROOT.locate(Term::App(a_app));

        println!("创建链式绑定:");

        // X0 -> X1
        println!("1. X0/l1 -> X1/l1");
        let success1 = subst.unify(x0_l1, x1_l1);
        println!("   统一成功: {}", success1);
        println!("   替换: {}", subst);

        // X1 -> a
        println!("2. X1/l1 -> a/l0");
        let success2 = subst.unify(x1_l1, a_l0);
        println!("   统一成功: {}", success2);
        println!("   替换: {}", subst);

        // 测试查找
        println!("\n查找测试:");
        let lookup_x0 = subst.lookup(x0_l1);
        let lookup_x1 = subst.lookup(x1_l1);

        println!("查找 X0/l1: {}", lookup_x0);
        println!("查找 X1/l1: {}", lookup_x1);

        // 使用 Substituted 显示最终结果
        println!(
            "X0 经过替换后: {}",
            Substituted {
                substitution: &subst,
                item: x0_l1
            }
        );
        println!(
            "X1 经过替换后: {}",
            Substituted {
                substitution: &subst,
                item: x1_l1
            }
        );
    }

    #[test]
    fn test_literal_unification() {
        println!("\n=== 测试文字统一 ===");

        let mut subst = Substitution::default();
        let (_, _, a_symbol) = create_test_symbols();

        // 创建谓词符号 P(x)
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        let loc1 = Location::new(1);
        let loc2 = Location::new(2);

        // 创建 P(X0) 和 P(a)
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        let px_app = Perfect::new(Application {
            symbol: p_symbol,
            args: vec![Term::Var(0)].into(),
            ground: false,
        });

        let pa_app = Perfect::new(Application {
            symbol: p_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        let px_literal = Literal {
            polarity: true,
            atom: px_app,
        };

        let pa_literal = Literal {
            polarity: true,
            atom: pa_app,
        };

        let px_located = loc1.locate(px_literal);
        let pa_located = loc2.locate(pa_literal);

        println!("统一文字 P(X0)/l1 和 P(a)/l2");

        let success = subst.connect(px_located, pa_located);
        println!("连接成功: {}", success);
        println!("替换结果: {}", subst);

        // 显示统一后的文字
        println!(
            "统一后的 P(X0): {}",
            Substituted {
                substitution: &subst,
                item: px_located
            }
        );
        println!(
            "统一后的 P(a): {}",
            Substituted {
                substitution: &subst,
                item: pa_located
            }
        );
    }

    #[test]
    fn test_equality_handling() {
        println!("\n=== 测试等式处理 ===");

        let mut subst = Substitution::default();
        let (_, _, a_symbol) = create_test_symbols();

        // 创建等式符号
        let eq_symbol = Perfect::new(Symbol {
            arity: 2,
            sort: Sort::Bool,
            name: Name::Equality,
        });

        let loc1 = Location::new(1);

        // 创建常量 a 和 b
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        let b_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("b"),
        });

        let b_app = Perfect::new(Application {
            symbol: b_symbol,
            args: vec![].into(),
            ground: true,
        });

        // 创建等式 X0 = a
        let eq_app = Perfect::new(Application {
            symbol: eq_symbol,
            args: vec![Term::Var(0), Term::App(a_app)].into(),
            ground: false,
        });

        let eq_literal = Literal {
            polarity: true,
            atom: eq_app,
        };

        let eq_located = loc1.locate(eq_literal);

        println!(
            "等式文字: {}",
            Substituted {
                substitution: &subst,
                item: eq_located
            }
        );

        // 测试相等性检查
        let x0_l1 = loc1.locate(Term::Var(0));
        let a_l0 = ROOT.locate(Term::App(a_app));
        let b_l0 = ROOT.locate(Term::App(b_app));

        println!("\n测试相等性:");
        println!("X0/l1 和 a/l0 相等: {}", subst.equal(x0_l1, a_l0));
        println!("a/l0 和 b/l0 相等: {}", subst.equal(a_l0, b_l0));

        // 添加绑定后再测试
        subst.unify(x0_l1, a_l0);
        println!("\n添加绑定 X0 -> a 后:");
        println!("替换: {}", subst);
        println!("X0/l1 和 a/l0 相等: {}", subst.equal(x0_l1, a_l0));
    }

    #[test]
    fn test_substitution_workflow() {
        println!("\n=== 替换工作流程完整演示 ===");

        println!(
            "
这个测试演示了替换系统的完整工作流程:

1. 创建替换对象
2. 进行项的统一
3. 处理变量绑定
4. 查找和解引用
5. 显示最终结果

替换系统的核心功能:
- 统一(Unification): 使两个项相等的过程
- 绑定(Binding): 将变量绑定到具体的项
- 查找(Lookup): 解引用变量绑定链
- 出现检查(Occurs Check): 防止无限循环绑定
- 位置感知: 考虑项在连接表中的位置
        "
        );

        let mut subst = Substitution::default();
        let (f_symbol, g_symbol, a_symbol) = create_test_symbols();

        println!("\n=== 步骤1: 初始状态 ===");
        println!("替换: {}", subst);

        println!("\n=== 步骤2: 简单变量绑定 ===");
        let loc1 = Location::new(1);
        let x0_l1 = loc1.locate(Term::Var(0));
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });
        let a_l0 = ROOT.locate(Term::App(a_app));

        subst.unify(x0_l1, a_l0);
        println!("绑定 X0/l1 -> a/l0");
        println!("替换: {}", subst);

        println!("\n=== 步骤3: 复杂项统一 ===");
        let loc2 = Location::new(2);

        // 创建 f(X1) 和 f(g(a, X0))
        let fx1_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::Var(1)].into(),
            ground: false,
        });

        let gax0_app = Perfect::new(Application {
            symbol: g_symbol,
            args: vec![Term::App(a_app), Term::Var(0)].into(),
            ground: false,
        });

        let fgax0_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::App(gax0_app)].into(),
            ground: false,
        });

        let fx1_l2 = loc2.locate(Term::App(fx1_app));
        let fgax0_l1 = loc1.locate(Term::App(fgax0_app));

        println!("统一 f(X1)/l2 和 f(g(a, X0))/l1");
        subst.unify(fx1_l2, fgax0_l1);
        println!("替换: {}", subst);

        println!("\n=== 步骤4: 查看最终结果 ===");
        println!("所有绑定:");
        for (var, term) in subst.bindings() {
            println!("  {} -> {}", var, term);
        }

        println!("\n应用替换后的项:");
        println!(
            "X0/l1: {}",
            Substituted {
                substitution: &subst,
                item: x0_l1
            }
        );
        println!(
            "X1/l2: {}",
            Substituted {
                substitution: &subst,
                item: loc2.locate(Term::Var(1))
            }
        );
        println!(
            "f(X1)/l2: {}",
            Substituted {
                substitution: &subst,
                item: fx1_l2
            }
        );
        println!(
            "f(g(a, X0))/l1: {}",
            Substituted {
                substitution: &subst,
                item: fgax0_l1
            }
        );

        println!("\n=== 工作流程总结 ===");
        println!("1. X0 被绑定到常量 a");
        println!("2. X1 被绑定到复合项 g(a, X0)");
        println!("3. 由于 X0 已绑定到 a，所以 X1 实际上绑定到 g(a, a)");
        println!("4. 所有变量都得到了具体的值，统一成功完成");
    }
}
