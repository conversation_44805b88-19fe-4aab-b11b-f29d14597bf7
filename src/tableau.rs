use fnv::{Fnv<PERSON><PERSON><PERSON><PERSON><PERSON>, FnvHashMap};
use indexmap::IndexMap;

use crate::subst::{Branch, Location, ROOT};
use crate::syntax::Clause;

#[derive(<PERSON>lone, Co<PERSON>)]
pub(crate) struct Node {
    pub(crate) branch: Branch,
    pub(crate) depth: usize,
    pub(crate) clause: &'static Clause,
}

#[derive(Default)]
pub(crate) struct Tableau {
    map: FnvHashMap<Branch, Location>,
    nodes: IndexMap<Location, Node, FnvBuildHasher>,
}

impl std::ops::Index<Location> for Tableau {
    type Output = Node;
    fn index(&self, location: Location) -> &Node {
        &self.nodes[&location]
    }
}

impl Tableau {
    pub(crate) fn is_empty(&self) -> bool {
        self.len() == 0
    }

    pub(crate) fn len(&self) -> usize {
        self.nodes.len()
    }

    pub(crate) fn clear(&mut self) {
        self.nodes.clear();
    }

    pub(crate) fn truncate(&mut self, to: usize) {
        self.nodes.truncate(to);
    }

    pub(crate) fn contains(&self, location: Location) -> bool {
        self.nodes.contains_key(&location)
    }

    pub(crate) fn locate(&mut self, branch: Branch) -> Location {
        let next = Location::new(self.map.len() + 1);
        *self.map.entry(branch).or_insert(next)
    }

    pub(crate) fn locations(&self) -> impl Iterator<Item = Location> {
        self.nodes.keys().copied()
    }

    pub(crate) fn set_root_clause(&mut self, clause: &'static Clause) {
        let replaced = self.nodes.insert(
            ROOT,
            Node {
                branch: Branch {
                    location: Location::new(usize::MAX),
                    index: usize::MAX,
                },
                depth: 0,
                clause,
            },
        );
        assert!(replaced.is_none());
    }

    pub(crate) fn add_clause(&mut self, branch: Branch, clause: &'static Clause) {
        let depth = self[branch.location].depth + 1;
        let location = self.locate(branch);
        let replaced = self.nodes.insert(
            location,
            Node {
                branch,
                depth,
                clause,
            },
        );
        assert!(replaced.is_none());
    }

    pub(crate) fn graphviz(&self) {
        println!("digraph tableau {{");
        println!("\tnode [shape=none];");
        println!("\tl{}_{} [label=\"\"];", usize::MAX, usize::MAX);
        for (location, node) in &self.nodes {
            for (index, literal) in node.clause.literals.iter().enumerate() {
                let branch = Branch {
                    location: *location,
                    index,
                };
                println!("\t{branch} [label=\"{literal}\"];");
                println!("\t{} -> {};", node.branch, branch);
            }
        }
        println!("}}");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::builder::Builder;
    use crate::syntax::*;
    use crate::util::Perfect;
    use std::rc::Rc;
    use std::sync::Arc;

    // 创建测试用的符号
    fn create_test_symbols() -> (Perfect<Symbol>, Perfect<Symbol>, Perfect<Symbol>) {
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        let q_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("Q"),
        });

        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        (p_symbol, q_symbol, a_symbol)
    }

    // 创建简单的测试矩阵
    fn create_test_matrix() -> Matrix {
        let mut builder = Builder::default();
        let (p_symbol, q_symbol, a_symbol) = create_test_symbols();

        // 创建常量 a
        let a_app_rc = Rc::new(RcApplication {
            symbol: a_symbol,
            args: vec![].into(),
        });

        // 创建 P(a)
        let pa_app_rc = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![RcTerm::Application(a_app_rc.clone())].into(),
        });

        // 创建 Q(a)
        let qa_app_rc = Rc::new(RcApplication {
            symbol: q_symbol,
            args: vec![RcTerm::Application(a_app_rc)].into(),
        });

        // 子句1: P(a)
        builder.rc_clause(
            vec![RcLiteral {
                polarity: true,
                atom: pa_app_rc,
            }],
            Info {
                negated: false,
                is_goal: false,
                number: 0,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom1".to_string()),
                    original: Some(Arc::new("P(a)".to_string())),
                },
            },
        );

        // 子句2: ¬Q(a) (目标的否定)
        builder.rc_clause(
            vec![RcLiteral {
                polarity: false,
                atom: qa_app_rc,
            }],
            Info {
                negated: true,
                is_goal: true,
                number: 1,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("goal".to_string()),
                    original: Some(Arc::new("Q(a)".to_string())),
                },
            },
        );

        builder.finish()
    }

    #[test]
    fn test_tableau_basics() {
        println!("=== 测试连接表基础操作 ===");

        let mut tableau = Tableau::default();

        println!("初始状态:");
        println!("  是否为空: {}", tableau.is_empty());
        println!("  大小: {}", tableau.len());

        // 创建测试矩阵
        let matrix = create_test_matrix();
        let clause1 = &matrix.clauses[0];
        let clause2 = &matrix.clauses[1];

        println!("\n测试子句:");
        println!("  子句1: {}", clause1);
        println!("  子句2: {}", clause2);

        // 设置根子句
        println!("\n--- 设置根子句 ---");
        tableau.set_root_clause(clause2);
        println!("设置根子句后:");
        println!("  大小: {}", tableau.len());
        println!("  包含根位置: {}", tableau.contains(ROOT));

        let root_node = &tableau[ROOT];
        println!("  根节点信息:");
        println!("    分支: {}", root_node.branch);
        println!("    深度: {}", root_node.depth);
        println!("    子句: {}", root_node.clause);

        // 添加子句
        println!("\n--- 添加子句 ---");
        let branch = Branch {
            location: ROOT,
            index: 0,
        };

        tableau.add_clause(branch, clause1);
        println!("添加子句后:");
        println!("  大小: {}", tableau.len());

        // 显示所有位置
        println!("  所有位置:");
        for location in tableau.locations() {
            let node = &tableau[location];
            println!(
                "    {}: 深度={}, 子句={}",
                location, node.depth, node.clause
            );
        }
    }

    #[test]
    fn test_branch_location_mapping() {
        println!("\n=== 测试分支位置映射 ===");

        let mut tableau = Tableau::default();

        // 创建不同的分支
        let branches = vec![
            Branch {
                location: ROOT,
                index: 0,
            },
            Branch {
                location: ROOT,
                index: 1,
            },
            Branch {
                location: Location::new(1),
                index: 0,
            },
            Branch {
                location: Location::new(1),
                index: 1,
            },
            Branch {
                location: Location::new(2),
                index: 0,
            },
        ];

        println!("分支到位置的映射:");
        for (i, branch) in branches.iter().enumerate() {
            let location = tableau.locate(*branch);
            println!("  分支{} {} -> 位置 {}", i, branch, location);
        }

        // 测试重复映射
        println!("\n重复映射测试:");
        let branch1 = Branch {
            location: ROOT,
            index: 0,
        };
        let location1 = tableau.locate(branch1);
        let location2 = tableau.locate(branch1);
        println!("  同一分支 {} 映射到:", branch1);
        println!("    第一次: {}", location1);
        println!("    第二次: {}", location2);
        println!("    相同: {}", location1 == location2);
    }

    #[test]
    fn test_tableau_construction_process() {
        println!("\n=== 测试连接表构造过程 ===");

        let mut tableau = Tableau::default();
        let matrix = create_test_matrix();

        println!("构造过程演示:");
        println!("目标: 证明 P(a) ∧ ¬Q(a) 是不一致的");
        println!("子句集合:");
        for (i, clause) in matrix.clauses.iter().enumerate() {
            println!("  子句{}: {}", i, clause);
        }

        println!("\n=== 步骤1: 设置根子句(目标的否定) ===");
        let goal_clause = &matrix.clauses[1]; // ¬Q(a)
        tableau.set_root_clause(goal_clause);

        println!("根子句: {}", goal_clause);
        println!("连接表状态:");
        print_tableau_state(&tableau);

        println!("\n=== 步骤2: 扩展第一个分支 ===");
        // 为根子句的第一个文字创建分支
        let root_branch = Branch {
            location: ROOT,
            index: 0, // ¬Q(a) 的索引
        };

        // 添加能够关闭这个分支的子句
        let axiom_clause = &matrix.clauses[0]; // P(a)
        tableau.add_clause(root_branch, axiom_clause);

        println!("扩展分支 {} 用子句: {}", root_branch, axiom_clause);
        println!("连接表状态:");
        print_tableau_state(&tableau);

        println!("\n=== 步骤3: 分析连接表结构 ===");
        println!("连接表深度分析:");
        for location in tableau.locations() {
            let node = &tableau[location];
            println!(
                "  位置 {}: 深度={}, 来自分支={}",
                location, node.depth, node.branch
            );
        }

        println!("\n可能的连接:");
        // 在实际搜索中，这里会检查哪些文字可以统一
        println!("  根位置的 ¬Q(a) 需要找到 Q(a) 来关闭");
        println!("  但当前只有 P(a)，无法直接关闭");
        println!("  需要更多子句或不同的搜索策略");
    }

    #[test]
    fn test_complex_tableau_construction() {
        println!("\n=== 测试复杂连接表构造 ===");

        // 创建更复杂的矩阵: P(a) ∨ Q(a), ¬P(a) ∨ R(a), ¬Q(a) ∨ R(a), ¬R(a)
        let matrix = create_complex_test_matrix();
        let mut tableau = Tableau::default();

        println!("复杂证明问题:");
        println!("子句集合:");
        for (i, clause) in matrix.clauses.iter().enumerate() {
            println!("  子句{}: {}", i, clause);
        }

        println!("\n=== 构造步骤 ===");

        // 步骤1: 设置根子句 ¬R(a)
        println!("步骤1: 设置根子句");
        let goal_clause = &matrix.clauses[3]; // ¬R(a)
        tableau.set_root_clause(goal_clause);
        println!("  根子句: {}", goal_clause);
        print_tableau_state(&tableau);

        // 步骤2: 扩展根分支
        println!("\n步骤2: 扩展根分支");
        let root_branch = Branch {
            location: ROOT,
            index: 0,
        };
        let clause2 = &matrix.clauses[2]; // ¬Q(a) ∨ R(a)
        tableau.add_clause(root_branch, clause2);
        println!("  扩展分支 {} 用子句: {}", root_branch, clause2);
        print_tableau_state(&tableau);

        // 步骤3: 继续扩展
        println!("\n步骤3: 继续扩展");
        let l1_location = tableau.locate(root_branch);
        let l1_branch = Branch {
            location: l1_location,
            index: 0,
        };
        let clause0 = &matrix.clauses[0]; // P(a) ∨ Q(a)
        tableau.add_clause(l1_branch, clause0);
        println!("  扩展分支 {} 用子句: {}", l1_branch, clause0);
        print_tableau_state(&tableau);

        // 步骤4: 最后扩展
        println!("\n步骤4: 最后扩展");
        let l2_location = tableau.locate(l1_branch);
        let l2_branch = Branch {
            location: l2_location,
            index: 0,
        };
        let clause1 = &matrix.clauses[1]; // ¬P(a) ∨ R(a)
        tableau.add_clause(l2_branch, clause1);
        println!("  扩展分支 {} 用子句: {}", l2_branch, clause1);
        print_tableau_state(&tableau);

        println!("\n=== 最终连接表分析 ===");
        analyze_tableau_structure(&tableau);
    }

    #[test]
    fn test_tableau_operations() {
        println!("\n=== 测试连接表操作 ===");

        let mut tableau = Tableau::default();
        let matrix = create_test_matrix();

        // 测试清空操作
        println!("测试清空操作:");
        tableau.set_root_clause(&matrix.clauses[0]);
        println!("  添加根子句后大小: {}", tableau.len());

        tableau.clear();
        println!("  清空后大小: {}", tableau.len());
        println!("  是否为空: {}", tableau.is_empty());

        // 测试截断操作
        println!("\n测试截断操作:");
        tableau.set_root_clause(&matrix.clauses[0]);
        let branch1 = Branch {
            location: ROOT,
            index: 0,
        };
        tableau.add_clause(branch1, &matrix.clauses[1]);
        let branch2 = Branch {
            location: tableau.locate(branch1),
            index: 0,
        };
        tableau.add_clause(branch2, &matrix.clauses[0]);

        println!("  添加多个子句后大小: {}", tableau.len());

        tableau.truncate(2);
        println!("  截断到2后大小: {}", tableau.len());

        // 测试位置检查
        println!("\n测试位置检查:");
        println!("  包含根位置: {}", tableau.contains(ROOT));
        println!("  包含位置1: {}", tableau.contains(Location::new(1)));
        println!("  包含位置999: {}", tableau.contains(Location::new(999)));
    }

    #[test]
    fn test_graphviz_output() {
        println!("\n=== 测试Graphviz输出 ===");

        let mut tableau = Tableau::default();
        let matrix = create_complex_test_matrix();

        // 构建一个小的连接表
        tableau.set_root_clause(&matrix.clauses[3]); // ¬R(a)
        let branch1 = Branch {
            location: ROOT,
            index: 0,
        };
        tableau.add_clause(branch1, &matrix.clauses[2]); // ¬Q(a) ∨ R(a)

        println!("连接表的Graphviz表示:");
        tableau.graphviz();

        println!("\n说明:");
        println!("- 每个节点表示一个文字");
        println!("- 边表示父子关系");
        println!("- 可以用Graphviz工具可视化连接表结构");
    }

    #[test]
    fn test_tableau_workflow() {
        println!("\n=== 连接表方法工作流程演示 ===");

        println!(
            "
连接表方法是一种系统化的定理证明技术，工作流程如下:

1. 问题转换: 将要证明的定理转换为子句集合
2. 目标否定: 将目标公式否定并加入子句集合
3. 连接表构建: 系统地构建连接表树结构
4. 分支扩展: 为每个开放分支寻找可以关闭它的子句
5. 统一检查: 检查文字是否可以统一
6. 归约检查: 检查是否可以与祖先节点归约
7. 证明完成: 当所有分支都关闭时，证明完成

连接表的核心概念:
- 节点(Node): 包含子句、分支信息和深度
- 分支(Branch): 指向特定位置的特定文字
- 位置(Location): 连接表中的唯一标识
- 深度(Depth): 节点在树中的层级
        "
        );

        let mut tableau = Tableau::default();
        let matrix = create_test_matrix();

        println!("\n=== 实际演示 ===");
        println!("问题: 证明 {{P(a), ¬Q(a)}} 是不一致的");

        println!("\n步骤1: 初始化空连接表");
        println!("  大小: {}", tableau.len());

        println!("\n步骤2: 设置根子句(目标否定)");
        tableau.set_root_clause(&matrix.clauses[1]); // ¬Q(a)
        println!("  根子句: {}", matrix.clauses[1]);
        println!("  大小: {}", tableau.len());

        println!("\n步骤3: 分析开放分支");
        let root_node = &tableau[ROOT];
        println!("  根节点有 {} 个文字", root_node.clause.literals.len());
        for (i, literal) in root_node.clause.literals.iter().enumerate() {
            let branch = Branch {
                location: ROOT,
                index: i,
            };
            println!("    分支 {}: {}", branch, literal);
        }

        println!("\n步骤4: 扩展分支(在实际搜索中会自动进行)");
        println!("  需要找到包含 Q(a) 的子句来关闭 ¬Q(a)");
        println!("  当前可用子句: P(a)");
        println!("  无法直接关闭，需要更复杂的推理");

        println!("\n连接表方法的优势:");
        println!("- 系统化: 确保不遗漏任何可能的推理路径");
        println!("- 完备性: 如果存在证明，一定能找到");
        println!("- 结构化: 树状结构便于理解和调试");
        println!("- 回溯支持: 支持搜索失败时的回溯");
    }

    // 辅助函数：打印连接表状态
    fn print_tableau_state(tableau: &Tableau) {
        println!("  连接表大小: {}", tableau.len());
        for location in tableau.locations() {
            let node = &tableau[location];
            println!(
                "    位置 {}: 深度={}, 分支={}, 子句={}",
                location, node.depth, node.branch, node.clause
            );
        }
    }

    // 辅助函数：分析连接表结构
    fn analyze_tableau_structure(tableau: &Tableau) {
        println!("连接表结构分析:");
        println!("  总节点数: {}", tableau.len());

        let mut depth_count = std::collections::HashMap::new();
        for location in tableau.locations() {
            let node = &tableau[location];
            *depth_count.entry(node.depth).or_insert(0) += 1;
        }

        println!("  深度分布:");
        for (depth, count) in depth_count {
            println!("    深度 {}: {} 个节点", depth, count);
        }

        println!("  节点详情:");
        for location in tableau.locations() {
            let node = &tableau[location];
            println!(
                "    {}: 来自 {} (深度 {})",
                location, node.branch, node.depth
            );
            for (i, literal) in node.clause.literals.iter().enumerate() {
                let branch = Branch { location, index: i };
                println!("      分支 {}: {}", branch, literal);
            }
        }
    }

    // 创建复杂测试矩阵
    fn create_complex_test_matrix() -> Matrix {
        let mut builder = Builder::default();
        let (p_symbol, q_symbol, a_symbol) = create_test_symbols();

        // 创建 R 符号
        let r_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("R"),
        });

        let a_app_rc = Rc::new(RcApplication {
            symbol: a_symbol,
            args: vec![].into(),
        });

        let pa_app_rc = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![RcTerm::Application(a_app_rc.clone())].into(),
        });

        let qa_app_rc = Rc::new(RcApplication {
            symbol: q_symbol,
            args: vec![RcTerm::Application(a_app_rc.clone())].into(),
        });

        let ra_app_rc = Rc::new(RcApplication {
            symbol: r_symbol,
            args: vec![RcTerm::Application(a_app_rc)].into(),
        });

        // 子句0: P(a) ∨ Q(a)
        builder.rc_clause(
            vec![
                RcLiteral {
                    polarity: true,
                    atom: pa_app_rc.clone(),
                },
                RcLiteral {
                    polarity: true,
                    atom: qa_app_rc.clone(),
                },
            ],
            Info {
                negated: false,
                is_goal: false,
                number: 0,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom1".to_string()),
                    original: Some(Arc::new("P(a) | Q(a)".to_string())),
                },
            },
        );

        // 子句1: ¬P(a) ∨ R(a)
        builder.rc_clause(
            vec![
                RcLiteral {
                    polarity: false,
                    atom: pa_app_rc,
                },
                RcLiteral {
                    polarity: true,
                    atom: ra_app_rc.clone(),
                },
            ],
            Info {
                negated: false,
                is_goal: false,
                number: 1,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom2".to_string()),
                    original: Some(Arc::new("~P(a) | R(a)".to_string())),
                },
            },
        );

        // 子句2: ¬Q(a) ∨ R(a)
        builder.rc_clause(
            vec![
                RcLiteral {
                    polarity: false,
                    atom: qa_app_rc,
                },
                RcLiteral {
                    polarity: true,
                    atom: ra_app_rc.clone(),
                },
            ],
            Info {
                negated: false,
                is_goal: false,
                number: 2,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom3".to_string()),
                    original: Some(Arc::new("~Q(a) | R(a)".to_string())),
                },
            },
        );

        // 子句3: ¬R(a)
        builder.rc_clause(
            vec![RcLiteral {
                polarity: false,
                atom: ra_app_rc,
            }],
            Info {
                negated: true,
                is_goal: true,
                number: 3,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("goal".to_string()),
                    original: Some(Arc::new("R(a)".to_string())),
                },
            },
        );

        builder.finish()
    }
}
