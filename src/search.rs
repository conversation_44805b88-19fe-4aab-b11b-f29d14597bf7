use crate::syntax::IsGround;
use fnv::{FnvBuildHasher, FnvHashSet};
use indexmap::IndexSet;
use rand::rngs::SmallRng;
use rand::seq::SliceRandom;
use rand::{Rng, SeedableRng};
use std::io::Write;

use crate::db::{Atom, DB};
use crate::options::Options;
use crate::subst::{Branch, Located, ROOT, Substituted, Substitution};
use crate::syntax::{Clause, Extension, Literal, Matrix, Name, Source, Term};
use crate::tableau::Tableau;
use crate::tstp;

// 用于恢复搜索状态的还原点:
// 包含多个用于截断内部数据的索引
// 在回溯时使用这些索引来恢复到之前的状态
#[derive(Debug, Default, Clone, Copy)]
struct Restore {
    tableau: usize,      // 连接表的大小
    substitution: usize, // 替换的大小
    trail: usize,        // 踪迹的大小
    closed: usize,       // 已关闭分支的数量
}

// 证明搜索所需的全部数据结构
pub(crate) struct Search<'matrix> {
    matrix: &'matrix Matrix, // 输入的公式矩阵
    // 用于随机选择开放分支的随机数生成器
    rng: SmallRng,
    // 字面量构成的证明树结构(连接表)
    tableau: Tableau,
    // 全局统一替换
    substitution: Substitution,
    // 变量踪迹:有序的哈希集合，用于记录搜索过程中的变量绑定和约束
    trail: IndexSet<Atom, FnvBuildHasher>,
    // 已关闭的分支: 用于open分支的回溯
    closed: Vec<Branch>,
    // 每一步后的还原点，用于回溯
    restore: Vec<Restore>,
    // 当前开放的分支集合(无特定顺序)
    open: Vec<Branch>,
    // 当前正在学习的子句
    // 使用IndexSet而不是HashSet是为了保持迭代顺序的一致性
    learn: IndexSet<Atom, FnvBuildHasher>,
    // 在任何推理尝试中，踪迹的上一个长度
    // 用于确定应该学习哪些原子
    learn_from: usize,
    // 已学习子句的集合
    db: DB,
    // 当前迭代加深的深度限制
    depth: usize,
    // 用于检查统一性的临时替换
    scratch: Substitution,
    // 用于随机打乱扩展规则的临时空间
    extensions: Vec<Extension>,
}

impl<'matrix> Search<'matrix> {
    // jump back to a particular restore point
    fn restore(&mut self, restore: Restore) {
        self.tableau.truncate(restore.tableau);
        self.substitution.truncate(restore.substitution);
        self.trail.truncate(restore.trail);
        self.open.extend(self.closed.drain(restore.closed..));
        self.open
            .retain(|branch| self.tableau.contains(branch.location));
    }

    // the beginning of proof search
    pub(crate) fn new(matrix: &'matrix Matrix, depth: usize) -> Self {
        Self {
            matrix,
            rng: SmallRng::seed_from_u64(0),
            tableau: Tableau::default(),
            substitution: Substitution::default(),
            trail: IndexSet::default(),
            closed: vec![],
            restore: vec![],
            open: vec![],
            learn: IndexSet::default(),
            learn_from: 0,
            db: DB::default(),
            depth,
            scratch: Substitution::default(),
            extensions: vec![],
        }
    }

    // 检查证明是否完成
    pub(crate) fn is_closed(&self) -> bool {
        // open集合在开始时为空，所以我们还需要检查是否选择了某个起始子句
        // 只有当restore不为空(表示已经开始搜索)且open为空(表示所有分支都已关闭)时，才算完成证明
        !self.restore.is_empty() && self.open.is_empty()
    }

    // 使用graphviz导出证明树的可视化表示，用于检查证明
    pub(crate) fn graphviz(&self) {
        self.tableau.graphviz()
    }

    // 以TSTP(Thousands of Solutions from Theorem Provers)格式打印证明
    // 假定已经找到一个证明
    pub(crate) fn tstp<W: Write>(&self, w: &mut W, options: &Options) -> anyhow::Result<()> {
        assert!(self.is_closed());
        tstp::theorem(w, options)?;
        writeln!(w, "% SZS output start CNFRefutation")?;

        let mut input_clauses = FnvHashSet::default();
        let mut input_formulas = FnvHashSet::default();
        for location in self.tableau.locations() {
            let node = self.tableau[location];
            if !matches!(node.clause.info.source, Source::Axiom { .. }) {
                continue;
            }
            let number = node.clause.info.number;
            if input_clauses.insert(number)
                && let Source::Axiom {
                    path,
                    name,
                    original,
                } = &node.clause.info.source
            {
                if input_formulas.insert((path.clone(), name.clone()))
                    && let Some(original) = original
                {
                    writeln!(w, "fof({name}, plain, {original}, file({path}, {name})).")?;
                    if node.clause.info.negated {
                        writeln!(
                            w,
                            "fof({name}_negated, plain, ~({original}), inference(negate_conjecture, [status(cth)], [{name}]))."
                        )?;
                    }
                }
                write!(w, "cnf({}, plain, ", node.clause.info.number)?;
                let mut first = true;
                for literal in &node.clause.literals {
                    if !first {
                        write!(w, " | ")?;
                    }
                    first = false;
                    write!(w, "{literal}")?;
                }
                write!(w, ", inference(cnf, [status(esa)], [")?;
                write!(w, "{name}")?;
                if node.clause.info.negated {
                    write!(w, "_negated")?;
                }
                writeln!(w, "])).")?;
            }
            write!(w, "cnf({location}, plain, ")?;
            let mut first = true;
            for literal in &node.clause.literals {
                if !first {
                    write!(w, " | ")?;
                }
                first = false;
                write!(
                    w,
                    "{}",
                    Substituted {
                        substitution: &self.substitution,
                        item: location.locate(*literal),
                    }
                )?;
            }
            writeln!(w, ", inference(instantiation, [status(thm)], [{number}])).",)?;
        }
        write!(
            w,
            "cnf(final, plain, $false, inference(ground_refutation, [status(thm)], ["
        )?;
        let mut first = true;
        for location in self.tableau.locations() {
            let node = self.tableau[location];
            if !matches!(node.clause.info.source, Source::Axiom { .. }) {
                continue;
            }
            if !first {
                write!(w, ", ")?;
            }
            write!(w, "{location}")?;
            first = false;
        }
        writeln!(w, "])).")?;
        writeln!(w, "% SZS output end CNFRefutation")?;
        Ok(())
    }

    // 由外部迭代调用的主要推理函数:
    // 要么 (1) 执行一个推理步骤: 开始规则、归约规则或扩展规则
    // 要么 (2) 发现当前分支无法继续，执行回溯跳转
    // 返回值表示是否继续执行推理步骤
    // true表示应该继续推理，false表示应该停止(可能是找到证明或需要增加深度限制)
    pub(crate) fn step_or_backtrack(&mut self) -> bool {
        if self.is_closed() {
            return false; // 如果证明已完成则停止
        }

        if self.rng.random::<f32>() < 0.0001 {
            self.tableau.clear();
            self.substitution.clear();
            self.trail.clear();
            self.closed.clear();
            self.restore.clear();
            self.open.clear();
        }
        /*
        eprint!("trail:");
        for atom in &self.trail {
            eprint!(" {}", atom);
        }
        eprintln!();
        eprintln!("substitution: {}", self.substitution);
        */

        // 初始时为空的子句，因未应用以下规则而被填充。
        self.learn.clear();
        // log where we were before we start mutating things
        self.learn_from = self.trail.len();
        self.restore.push(Restore {
            tableau: self.tableau.len(),
            substitution: self.substitution.len(),
            trail: self.learn_from,
            closed: self.closed.len(),
        });

        if self.open.is_empty() {
            if self.try_start() {
                // start rule succeeded, we're done here
                return true;
            }
        } else {
            // 随机选择一个打开的分支并将其从 `open` 中移除。
            let index = self.rng.random_range(..self.open.len());
            let open = self.open.swap_remove(index);
            if self.try_close(open) {
                // closing the branch succeeded, we're done here
                self.closed.push(open);
                return true;
            }
            // failed to close, put it back
            self.open.push(open);

            // add literal itself to learned clause
            self.learn.insert(Atom::Place(
                open,
                self.tableau[open.location].clause.literals[open.index],
            ));
        }

        // all rules failed here
        self.restore.pop();

        /*
        eprint!("learn:");
        for reason in &self.learn {
            eprint!(" {}", reason);
        }
        eprintln!();
        */

        // all learned atoms should be in the trail
        assert!(self.learn.iter().all(|a| self.trail.contains(a)));

        // if the learned clause is empty, we need to increase depth limit
        if self.learn.is_empty() {
            return false;
        }

        // do the backjump
        // first, determine where in the trail the learned clause is falsified
        let conflict_position = self
            .trail
            .iter()
            .rposition(|atom| self.learn.contains(atom))
            .expect("live atoms do not contain learned clause");
        // work out which inference step that corresponds to
        let restore_position = self
            .restore
            .iter()
            .rposition(|restore| restore.trail <= conflict_position)
            .unwrap_or_default();

        // jump back to there
        let restore = self.restore[restore_position];
        self.restore.truncate(restore_position);
        self.restore(restore);

        // insert the learned clause to the database
        self.db.insert(self.learn.drain(..).collect(), &self.trail);
        true
    }

    // 尝试应用开始规则
    // 可能会失败，例如当已学习的子句阻止了这个操作
    // 开始规则是连接表证明中的第一步，选择一个起始子句开始构建证明树
    fn try_start(&mut self) -> bool {
        for start in &self.matrix.start {
            if self.start(start) {
                return true; // 成功应用开始规则
            }
        }
        false // 所有可能的起始子句都失败
    }

    // 尝试使用特定的起始子句开始证明
    // start子句通常是要证明的目标的否定
    fn start(&mut self, start: &'static Clause) -> bool {
        assert!(self.tableau.is_empty());
        assert!(self.substitution.is_empty());
        assert!(self.trail.is_empty());
        assert!(self.closed.is_empty());
        assert!(self.open.is_empty());

        // add the clause to the tableau
        self.tableau.set_root_clause(start);
        for (index, literal) in start.literals.iter().enumerate() {
            self.open.push(Branch {
                location: ROOT,
                index,
            });
            self.trail.insert(Atom::Place(
                Branch {
                    location: ROOT,
                    index,
                },
                *literal,
            ));
        }

        // add the disequations from the new clause
        self.trail.extend(
            start
                .disequations
                .iter()
                .map(|d| Atom::Disequation(ROOT.locate(d.left), ROOT.locate(d.right))),
        );
        // check that we didn't violate anything
        if !self.check_trail_consistency(0) {
            self.tableau.clear();
            self.trail.clear();
            self.open.clear();
            return false;
        }
        true
    }

    // 尝试通过归约规则或扩展规则关闭一个开放分支
    // 归约规则尝试将当前分支与其祖先节点进行统一
    // 扩展规则通过添加新的子句来扩展证明树
    fn try_close(&mut self, open: Branch) -> bool {
        // 首先尝试归约规则：向上遍历祖先节点
        // 归约规则寻找互补的文字对进行统一
        let mut ancestor = open;
        while ancestor.location != ROOT {
            ancestor = self.tableau[ancestor.location].branch;
            if self.reduce(ancestor, open) {
                return true; // 成功通过归约规则关闭分支
            }
        }

        let node = self.tableau[open.location];
        let Literal { polarity, atom } = node.clause.literals[open.index];
        // 当达到迭代加深的深度限制时，不能进行扩展
        // 当前文字不是基项(ground term，不含变量)
        if node.depth >= self.depth && !atom.is_ground() {
            return false;
        }

        // 强制执行正则性条件(regularity condition)
        // 正则性要求：在一个分支上，不能出现可统一的文字对
        let restore_trail = self.trail.len();
        let k = open.location.locate(node.clause.literals[open.index]);
        let mut ancestor = open;
        while ancestor.location != ROOT {
            ancestor = self.tableau[ancestor.location].branch;
            let member = self.tableau[ancestor.location];
            let l = ancestor
                .location
                .locate(member.clause.literals[ancestor.index]);
            if self.could_unify(l, k) {
                self.trail.insert(Atom::Disequation(
                    l.map(|l| Term::App(l.atom)),
                    k.map(|k| Term::App(k.atom)),
                ));
            }
        }

        // check we don't already violate regularity by extending rather than reducing
        if !self.check_trail_consistency(restore_trail) {
            self.trail.truncate(restore_trail);
            return false;
        }

        // 尝试应用扩展规则
        // 检查是否为不等式(在处理等式理论时需要特殊处理)
        let is_disequation = !polarity && matches!(atom.symbol.name, Name::Equality);
        self.extensions.clear();
        // 从矩阵中获取可能的扩展子句
        // 注意这里使用!polarity来寻找互补的文字
        self.extensions.extend(
            self.matrix
                .index
                .get(&(!polarity, atom.symbol))
                .into_iter()
                .flatten(),
        );
        // 随机打乱扩展规则的顺序以增加搜索的多样性
        self.extensions.shuffle(&mut self.rng);
        // 尝试每一个可能的扩展
        while let Some(extension) = self.extensions.pop() {
            if self.extend(open, extension, is_disequation) {
                return true; // 成功找到一个有效的扩展
            }
        }

        self.trail.truncate(restore_trail);
        false
    }

    // 尝试使用祖先节点归约当前开放的分支
    fn reduce(&mut self, ancestor: Branch, open: Branch) -> bool {
        let ancestor_node = self.tableau[ancestor.location];
        let open_node = self.tableau[open.location];
        let ancestor_literal = ancestor_node.clause.literals[ancestor.index];
        let open_literal = open_node.clause.literals[open.index];
        // 获取两个文字的定位版本，以便进行统一
        let l = ancestor.location.locate(ancestor_literal);
        let k = open.location.locate(open_literal);

        // 如果l永远无法归约k，我们需要给出简短的解释
        // 两种情况：1. 极性相同 2. 无法统一
        if ancestor_literal.polarity == open_literal.polarity || !self.could_unify(l, k) {
            self.learn.insert(Atom::CannotReduce(ancestor, open));
            return false;
        }

        // 保存当前替换的长度，以便在失败时撤销
        let restore_subst = self.substitution.len();
        // 尝试连接l和k：可能会失败，因为现有的替换可能阻止它们统一
        if !self.substitution.connect(l, k) {
            // learn from:
            // placement of `l` (already inserted)
            // placement of `k` (below)
            self.learn.insert(Atom::Place(ancestor, ancestor_literal));
            // ... and a series of variable bindings that prevented them unifying
            self.explain_connection_failure(l, k);
            return false;
        }

        let restore_trail = self.trail.len();
        // 重用scratch中的绑定作为一种传播:
        // 如果我们要连接l和k，就必须绑定一些变量
        // 注意：这与使用self.substitution中的新绑定不等价
        // 因为这些绑定可能是由之前的绑定隐含推导出来的
        self.trail
            .extend(self.scratch.bindings().map(|(x, t)| Atom::Bind(*x, *t)));

        // check that we didn't violate any previously-learned clause
        if !self.check_trail_consistency(restore_trail) {
            self.substitution.truncate(restore_subst);
            self.trail.truncate(restore_trail);
            return false;
        }
        true
    }

    // 尝试在开放分支处使用特定的扩展步骤进行扩展
    // extension包含了要添加的新子句以及要与当前文字统一的文字位置
    fn extend(&mut self, open: Branch, extension: Extension, is_disequation: bool) -> bool {
        let node = self.tableau[open.location];
        let open_literal = node.clause.literals[open.index];
        // l是当前开放分支的文字
        let l = open.location.locate(open_literal);
        // 为新子句分配位置
        let location = self.tableau.locate(open);
        // k是扩展子句中要与l统一的文字
        let k = location.locate(extension.clause.literals[extension.index]);
        // 如果l和k无法统一，则不需要继续考虑这个扩展步骤
        if !self.could_unify(l, k) {
            return false;
        }

        let restore_subst = self.substitution.len();
        // ...if however they could, but don't because of some bindings,
        if !self.substitution.connect(l, k) {
            // ...we should explain why
            self.explain_connection_failure(l, k);
            return false;
        }

        let restore_tableau = self.tableau.len();
        let restore_trail = self.trail.len();
        let restore_open = self.open.len();
        // add those bindings to the trail if successful
        self.trail
            .extend(self.scratch.bindings().map(|(x, t)| Atom::Bind(*x, *t)));
        // add the clause to the tableau...
        self.tableau.add_clause(open, extension.clause);
        for (index, literal) in extension.clause.literals.iter().enumerate() {
            let branch = Branch { location, index };
            self.trail
                .insert(Atom::Place(Branch { location, index }, *literal));
            if index != extension.index {
                self.open.push(branch)
            }

            // check whether it's possible to reduce this literal with anything on the path
            let mut ancestor = open;
            loop {
                let node = self.tableau[ancestor.location];
                let candidate = node.clause.literals[ancestor.index];
                if candidate.polarity == literal.polarity
                    || !self.could_unify(
                        ancestor.location.locate(candidate),
                        location.locate(*literal),
                    )
                {
                    // if not, add a "cannot reduce" atom
                    self.trail.insert(Atom::CannotReduce(ancestor, branch));
                }

                if ancestor.location == ROOT {
                    break;
                }
                ancestor = node.branch;
            }
        }
        // ...and its disequations to the trail
        self.trail.extend(
            extension
                .clause
                .disequations
                .iter()
                .map(|d| Atom::Disequation(location.locate(d.left), location.locate(d.right))),
        );

        let mut ancestor = open;
        while ancestor.location != ROOT {
            ancestor = self.tableau[ancestor.location].branch;
            let candidate = self.tableau[ancestor.location].clause.literals[ancestor.index];
            if candidate.polarity != open_literal.polarity
                && self.could_unify(ancestor.location.locate(candidate), l)
            {
                // if we could have reduced, we should have
                self.trail.insert(Atom::Disequation(
                    ancestor
                        .location
                        .locate(candidate)
                        .map(|l| Term::App(l.atom)),
                    l.map(|l| Term::App(l.atom)),
                ));
            }
        }

        // if we're extending a disequation, we can demand that either we close it with reflexivity
        // or the two sides are not syntactically equal
        if is_disequation && !matches!(extension.clause.info.source, Source::Reflexivity) {
            self.trail.insert(Atom::Disequation(
                l.map(|l| l.atom.args[0]),
                l.map(|l| l.atom.args[1]),
            ));
        }

        // something bad, give up on this one
        if !self.check_trail_consistency(restore_trail) {
            self.substitution.truncate(restore_subst);
            self.tableau.truncate(restore_tableau);
            self.trail.truncate(restore_trail);
            self.open.truncate(restore_open);
            return false;
        }

        true
    }

    // 检查踪迹与数据库(和实际约束)的一致性
    // 只检查after之后的条目
    // 这个函数实现了冲突驱动的学习机制
    fn check_trail_consistency(&mut self, after: usize) -> bool {
        // 检查新添加的原子是否导致冲突
        for atom in &self.trail[after..] {
            if let Some(conflicts) = self.db.find_conflicts(*atom, &self.trail) {
                // 如果发现冲突，将其添加到learn集合中
                // 如果有多个冲突，选择添加到learn中最少的那个
                // 这是为了获得最小的学习子句
                let chosen = conflicts
                    .iter()
                    .min_by_key(|conflict| {
                        conflict
                            .iter()
                            .filter(|a| {
                                self.trail.get_index_of(*a).unwrap() < self.learn_from
                                    && !self.learn.contains(*a)
                            })
                            .count()
                    })
                    .unwrap();
                // "resolve away" atoms occuring after `learn_from` in the trail
                self.learn.extend(
                    chosen
                        .iter()
                        .filter(|a| self.trail.get_index_of(*a).unwrap() < self.learn_from),
                );
                return false;
            }
        }

        // 检查到目前为止所有的不等式约束是否满足
        for (index, atom) in self.trail.iter().copied().enumerate() {
            let (left, right) = if let Atom::Disequation(left, right) = atom {
                (left, right)
            } else {
                continue;
            };
            if self.substitution.equal(left, right) {
                // eprintln!("failed disequation: {left} {right}");
                self.explain_equal(left, right);
                if index < self.learn_from {
                    self.learn.insert(atom);
                }
                return false;
            }
        }
        true
    }

    // 检查文字l和k是否有可能统一
    // 如果可以统一，self.scratch将包含它们的最一般统一项(Most General Unifier, MGU)
    // 注意：这个MGU有时会被其他函数使用！
    fn could_unify(&mut self, l: Located<Literal>, k: Located<Literal>) -> bool {
        self.scratch.clear();
        self.scratch.connect(l, k)
    }

    // 解释为什么l不能与k统一，使用trail[..learn_from]中的绑定来说明原因
    // 将导致不能统一的绑定插入到learn集合中
    // 这是冲突驱动学习的核心部分之一
    fn explain_connection_failure(&mut self, l: Located<Literal>, k: Located<Literal>) {
        self.scratch.clear();
        assert!(self.scratch.connect(l, k));

        // 首先绑定learn中的所有内容，因为从这些内容中我们不会得到任何新的信息
        // 这些都是已经确定需要学习的约束
        for atom in &self.learn {
            if let Atom::Bind(x, t) = *atom {
                if !self.scratch.unify(x.map(Term::Var), t) {
                    return;
                }
            }
        }

        // 这里我们假设在trail[learn_from..]中的绑定
        // 只来自于连接l和k的尝试
        'outer: loop {
            let reset = self.scratch.len();
            // try binding everything we didn't bind yet
            for atom in &self.trail[..self.learn_from] {
                let (x, t) = if let Atom::Bind(x, t) = *atom {
                    (x, t)
                } else {
                    continue;
                };
                if self.learn.contains(atom) {
                    continue;
                }
                if self.scratch.unify(x.map(Term::Var), t) {
                    continue;
                }
                // when we can't, the last atom we tried is part of the reason
                self.scratch.truncate(reset);
                self.learn.insert(*atom);
                // either we can now bind it, in which case carry on...
                if self.scratch.unify(x.map(Term::Var), t) {
                    continue 'outer;
                } else {
                    // ...or we can't and we're done
                    return;
                }
            }
            unreachable!()
        }
    }

    // explain why `left` is equal to `right` in terms of bindings from `trail[..learn_from]`
    // insert the result into `learn`
    fn explain_equal(&mut self, left: Located<Term>, right: Located<Term>) {
        self.scratch.clear();

        // 假设所有的绑定都是一致的
        // 预先绑定:
        // 1. learn_from之后的绑定，因为这些不能被学习
        // 2. learn中的绑定，因为这些已经确定要学习
        for atom in self.trail[self.learn_from..]
            .iter()
            .chain(self.learn.iter())
        {
            if let Atom::Bind(x, t) = *atom {
                assert!(self.scratch.unify(x.map(Term::Var), t));
            }
        }
        // already equal, done here
        if self.scratch.equal(left, right) {
            return;
        }

        'outer: loop {
            let reset = self.scratch.len();
            // 尝试绑定所有我们还没有绑定的变量
            for atom in &self.trail[..self.learn_from] {
                let (x, t) = if let Atom::Bind(x, t) = *atom {
                    (x, t)
                } else {
                    continue;
                };
                if self.learn.contains(atom) {
                    continue;
                }
                assert!(self.scratch.unify(x.map(Term::Var), t));
                if !self.scratch.equal(left, right) {
                    continue;
                }
                // 当我们不能在不使left和right相等的情况下进行绑定时，
                // 最后尝试的这个原子就是原因之一
                self.scratch.truncate(reset);
                self.learn.insert(*atom);
                assert!(self.scratch.unify(x.map(Term::Var), t));
                // either we can now bind it, in which case carry on...
                if self.scratch.equal(left, right) {
                    return;
                } else {
                    // ...or we can't and we're done
                    continue 'outer;
                }
            }
            unreachable!()
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::builder::Builder;
    use crate::syntax::*;
    use crate::util::Perfect;
    use std::sync::Arc;

    // 创建测试用的符号和应用
    fn create_test_symbols() -> (Perfect<Symbol>, Perfect<Symbol>, Perfect<Symbol>) {
        // 创建谓词符号 P(x)
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        // 创建谓词符号 Q(x)
        let q_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("Q"),
        });

        // 创建常量符号 a
        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        (p_symbol, q_symbol, a_symbol)
    }

    // 创建测试用的应用
    fn create_test_applications(
        p_symbol: Perfect<Symbol>,
        q_symbol: Perfect<Symbol>,
        a_symbol: Perfect<Symbol>,
    ) -> (
        Perfect<Application>,
        Perfect<Application>,
        Perfect<Application>,
    ) {
        // 常量 a
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        // P(a)
        let pa_app = Perfect::new(Application {
            symbol: p_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        // Q(a)
        let qa_app = Perfect::new(Application {
            symbol: q_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        (a_app, pa_app, qa_app)
    }

    // 创建一个简单的测试矩阵：P(a) ∧ ¬Q(a) → ⊥
    // 这表示 P(a) 和 ¬Q(a) 不能同时为真
    fn create_simple_test_matrix() -> Matrix {
        use std::rc::Rc;

        let mut builder = Builder::default();
        let (p_symbol, q_symbol, a_symbol) = create_test_symbols();

        // 创建 RcApplication 用于 RcLiteral
        let a_app_rc = Rc::new(RcApplication {
            symbol: a_symbol,
            args: vec![].into(),
        });

        let pa_app_rc = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![RcTerm::Application(a_app_rc.clone())].into(),
        });

        let qa_app_rc = Rc::new(RcApplication {
            symbol: q_symbol,
            args: vec![RcTerm::Application(a_app_rc)].into(),
        });

        // 创建子句1: P(a) (正文字)
        let clause1_literals = vec![RcLiteral {
            polarity: true,
            atom: pa_app_rc,
        }];

        let clause1_info = Info {
            negated: false,
            is_goal: false,
            number: 0,
            source: Source::Axiom {
                path: Arc::new("test".to_string()),
                name: Arc::new("axiom1".to_string()),
                original: Some(Arc::new("P(a)".to_string())),
            },
        };

        builder.rc_clause(clause1_literals, clause1_info);

        // 创建子句2: ¬Q(a) (负文字，作为目标的否定)
        let clause2_literals = vec![RcLiteral {
            polarity: false,
            atom: qa_app_rc,
        }];

        let clause2_info = Info {
            negated: true,
            is_goal: true,
            number: 1,
            source: Source::Axiom {
                path: Arc::new("test".to_string()),
                name: Arc::new("goal".to_string()),
                original: Some(Arc::new("Q(a)".to_string())),
            },
        };

        builder.rc_clause(clause2_literals, clause2_info);

        builder.finish()
    }

    #[test]
    fn test_search_initialization() {
        println!("=== 测试搜索初始化 ===");

        let matrix = create_simple_test_matrix();
        let search = Search::new(&matrix, 5);

        println!("矩阵包含 {} 个子句", matrix.clauses.len());
        println!("起始子句数量: {}", matrix.start.len());

        for (i, clause) in matrix.clauses.iter().enumerate() {
            println!("子句 {}: {}", i, clause);
        }

        println!("搜索初始状态:");
        println!("- 连接表为空: {}", search.tableau.is_empty());
        println!("- 替换为空: {}", search.substitution.is_empty());
        println!("- 踪迹为空: {}", search.trail.is_empty());
        println!("- 开放分支为空: {}", search.open.is_empty());
        println!("- 深度限制: {}", search.depth);
        println!("- 证明是否完成: {}", search.is_closed());
    }

    #[test]
    fn test_search_step_by_step() {
        println!("\n=== 测试搜索步骤演示 ===");

        let matrix = create_simple_test_matrix();
        let mut search = Search::new(&matrix, 5);

        println!("初始状态:");
        print_search_state(&search);

        let mut step = 0;
        while step < 10 {
            // 限制步数以避免无限循环
            step += 1;
            println!("\n--- 步骤 {} ---", step);

            let should_continue = search.step_or_backtrack();

            println!(
                "步骤结果: {}",
                if should_continue { "继续" } else { "停止" }
            );
            print_search_state(&search);

            if !should_continue {
                if search.is_closed() {
                    println!("🎉 找到证明！");
                } else {
                    println!("❌ 搜索失败或需要增加深度限制");
                }
                break;
            }
        }

        if step >= 10 {
            println!("⚠️  达到最大步数限制");
        }
    }

    // 创建一个更复杂的测试矩阵来演示更多搜索步骤
    // P(a) ∨ Q(a), ¬P(a) ∨ R(a), ¬Q(a) ∨ R(a), ¬R(a) → ⊥
    fn create_complex_test_matrix() -> Matrix {
        use std::rc::Rc;

        let mut builder = Builder::default();

        // 创建符号
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });
        let q_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("Q"),
        });
        let r_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("R"),
        });
        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        // 创建应用
        let a_app_rc = Rc::new(RcApplication {
            symbol: a_symbol,
            args: vec![].into(),
        });

        let pa_app_rc = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![RcTerm::Application(a_app_rc.clone())].into(),
        });

        let qa_app_rc = Rc::new(RcApplication {
            symbol: q_symbol,
            args: vec![RcTerm::Application(a_app_rc.clone())].into(),
        });

        let ra_app_rc = Rc::new(RcApplication {
            symbol: r_symbol,
            args: vec![RcTerm::Application(a_app_rc)].into(),
        });

        // 子句1: P(a) ∨ Q(a)
        builder.rc_clause(
            vec![
                RcLiteral {
                    polarity: true,
                    atom: pa_app_rc.clone(),
                },
                RcLiteral {
                    polarity: true,
                    atom: qa_app_rc.clone(),
                },
            ],
            Info {
                negated: false,
                is_goal: false,
                number: 0,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom1".to_string()),
                    original: Some(Arc::new("P(a) | Q(a)".to_string())),
                },
            },
        );

        // 子句2: ¬P(a) ∨ R(a)
        builder.rc_clause(
            vec![
                RcLiteral {
                    polarity: false,
                    atom: pa_app_rc,
                },
                RcLiteral {
                    polarity: true,
                    atom: ra_app_rc.clone(),
                },
            ],
            Info {
                negated: false,
                is_goal: false,
                number: 1,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom2".to_string()),
                    original: Some(Arc::new("~P(a) | R(a)".to_string())),
                },
            },
        );

        // 子句3: ¬Q(a) ∨ R(a)
        builder.rc_clause(
            vec![
                RcLiteral {
                    polarity: false,
                    atom: qa_app_rc,
                },
                RcLiteral {
                    polarity: true,
                    atom: ra_app_rc.clone(),
                },
            ],
            Info {
                negated: false,
                is_goal: false,
                number: 2,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("axiom3".to_string()),
                    original: Some(Arc::new("~Q(a) | R(a)".to_string())),
                },
            },
        );

        // 子句4: ¬R(a) (目标的否定)
        builder.rc_clause(
            vec![RcLiteral {
                polarity: false,
                atom: ra_app_rc,
            }],
            Info {
                negated: true,
                is_goal: true,
                number: 3,
                source: Source::Axiom {
                    path: Arc::new("test".to_string()),
                    name: Arc::new("goal".to_string()),
                    original: Some(Arc::new("R(a)".to_string())),
                },
            },
        );

        builder.finish()
    }

    #[test]
    fn test_complex_search() {
        println!("\n=== 测试复杂搜索演示 ===");

        let matrix = create_complex_test_matrix();
        let mut search = Search::new(&matrix, 10);

        println!("矩阵包含 {} 个子句:", matrix.clauses.len());
        for (i, clause) in matrix.clauses.iter().enumerate() {
            println!("  子句 {}: {}", i, clause);
        }
        println!("起始子句数量: {}", matrix.start.len());

        println!("\n初始状态:");
        print_search_state(&search);

        let mut step = 0;
        while step < 20 {
            // 增加步数限制
            step += 1;
            println!("\n--- 步骤 {} ---", step);

            let should_continue = search.step_or_backtrack();

            println!(
                "步骤结果: {}",
                if should_continue { "继续" } else { "停止" }
            );
            print_search_state(&search);

            if !should_continue {
                if search.is_closed() {
                    println!("🎉 找到证明！");
                } else {
                    println!("❌ 搜索失败或需要增加深度限制");
                }
                break;
            }
        }

        if step >= 20 {
            println!("⚠️  达到最大步数限制");
        }
    }

    #[test]
    fn test_search_explanation() {
        println!("\n=== 搜索过程详细解释 ===");

        println!(
            "
这个测试演示了连接表证明搜索的工作过程。

问题设置:
我们要证明从以下公理可以推导出矛盾:
1. P(a) ∨ Q(a)     (子句0: 要么P(a)为真，要么Q(a)为真)
2. ¬P(a) ∨ R(a)    (子句1: 如果P(a)为真，则R(a)为真)
3. ¬Q(a) ∨ R(a)    (子句2: 如果Q(a)为真，则R(a)为真)
4. ¬R(a)           (子句3: R(a)为假，这是目标的否定)

搜索过程解释:

步骤1 - 开始规则(Start Rule):
- 选择目标子句 ¬R(a) 作为起始点
- 将 ¬R(a) 添加到连接表的根节点 (l0)
- 创建开放分支 l0_0，表示需要关闭这个文字
- 踪迹记录: [~R(a)@l0_0]

步骤2 - 扩展规则(Extension Rule):
- 尝试关闭开放分支 l0_0 (¬R(a))
- 寻找包含 R(a) 的子句来与 ¬R(a) 统一
- 找到子句2: ¬Q(a) ∨ R(a)，选择 R(a) 进行扩展
- 添加子句2到连接表位置 l1，与 l0_0 连接
- 新的开放分支: l1_0 (¬Q(a))
- 关闭分支: l0_0 (通过与 l1_1 的 R(a) 统一)

步骤3 - 继续扩展:
- 尝试关闭开放分支 l1_0 (¬Q(a))
- 找到子句0: P(a) ∨ Q(a)，选择 Q(a) 进行扩展
- 添加子句0到连接表位置 l2
- 新的开放分支: l2_0 (P(a))
- 关闭分支: l1_0 (通过与 l2_1 的 Q(a) 统一)

步骤4 - 最后扩展:
- 尝试关闭开放分支 l2_0 (P(a))
- 找到子句1: ¬P(a) ∨ R(a)，选择 ¬P(a) 进行扩展
- 添加子句1到连接表位置 l3
- 新的开放分支: l3_1 (R(a))
- 关闭分支: l2_0 (通过与 l3_0 的 ¬P(a) 统一)

步骤5 - 归约规则(Reduction Rule):
- 尝试关闭开放分支 l3_1 (R(a))
- 发现可以与祖先节点 l0_0 的 ¬R(a) 进行归约
- R(a) 与 ¬R(a) 极性相反且可以统一
- 成功关闭最后一个开放分支
- 所有分支都已关闭，证明完成！

关键概念:
- 连接表(Tableau): 树状结构，每个节点包含一个子句
- 开放分支(Open Branch): 需要被关闭的文字
- 扩展规则: 添加新子句来尝试关闭开放分支
- 归约规则: 使用祖先节点来关闭当前分支
- 踪迹(Trail): 记录搜索过程中的所有约束和绑定
- 统一(Unification): 使两个文字相等的替换

符号说明:
- l0, l1, l2, l3: 连接表中的位置
- l0_0, l1_0, l2_0: 分支标识 (位置_文字索引)
- @: 文字在特定分支的位置
- ≁: 不能归约关系
        "
        );
    }

    // 辅助函数：打印搜索状态
    fn print_search_state(search: &Search) {
        println!("搜索状态:");
        println!("- 连接表大小: {}", search.tableau.len());
        println!("- 替换大小: {}", search.substitution.len());
        println!("- 踪迹大小: {}", search.trail.len());
        println!("- 开放分支数: {}", search.open.len());
        println!("- 已关闭分支数: {}", search.closed.len());
        println!("- 还原点数: {}", search.restore.len());
        println!("- 学习子句大小: {}", search.learn.len());
        println!("- 证明完成: {}", search.is_closed());

        if !search.trail.is_empty() {
            println!("踪迹内容:");
            for (i, atom) in search.trail.iter().enumerate() {
                println!("  [{}] {}", i, atom);
            }
        }

        if !search.open.is_empty() {
            println!("开放分支:");
            for (i, branch) in search.open.iter().enumerate() {
                println!(
                    "  [{}] 位置: {}, 索引: {}",
                    i, branch.location, branch.index
                );
            }
        }
    }
}
