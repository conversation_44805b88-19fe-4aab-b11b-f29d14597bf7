use crate::util::Perfect;
use fnv::{FnvHashMap, FnvHashSet};
use std::fmt;
use std::rc::Rc;
use std::sync::Arc;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
pub(crate) enum Sort {
    Bool,
    Obj,
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub(crate) enum Name {
    Equality,
    Atom(&'static str),
    Quoted(&'static str),
    Number(&'static str),
    Distinct(&'static str),
    Skolem(usize),
    Definition(usize),
}

impl fmt::Display for Name {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            Self::Equality => write!(f, "sPE"),
            Self::Atom(s) | Self::Number(s) => write!(f, "{s}"),
            Self::Quoted(quoted) => write!(f, "'{quoted}'"),
            Self::Distinct(distinct) => write!(f, "\"{distinct}\""),
            Self::Skolem(n) => write!(f, "sK{n}"),
            Self::Definition(n) => write!(f, "sP{n}"),
        }
    }
}

impl Name {
    pub(crate) fn needs_congruence(&self) -> bool {
        !matches!(self, Self::Equality | Self::Skolem(_) | Self::Definition(_))
    }
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub(crate) struct Symbol {
    pub(crate) arity: usize,
    pub(crate) sort: Sort,
    pub(crate) name: Name,
}

impl Symbol {
    pub(crate) fn is_equality(&self) -> bool {
        matches!(self.name, Name::Equality)
    }
}

pub(crate) trait IsGround {
    fn is_ground(&self) -> bool;
}

impl IsGround for usize {
    fn is_ground(&self) -> bool {
        false
    }
}

impl<T: IsGround> IsGround for Perfect<T> {
    fn is_ground(&self) -> bool {
        (**self).is_ground()
    }
}

#[derive(Debug, Hash, PartialEq, Eq)]
pub(crate) struct Application {
    pub(crate) symbol: Perfect<Symbol>,
    pub(crate) args: Box<[Term]>,
    pub(crate) ground: bool,
}

impl fmt::Display for Application {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.symbol.name)?;
        if !self.args.is_empty() {
            write!(f, "({}", &self.args[0])?;
            for arg in &self.args[1..] {
                write!(f, ",{arg}")?;
            }
            write!(f, ")")?;
        }
        Ok(())
    }
}

impl IsGround for Application {
    fn is_ground(&self) -> bool {
        self.ground
    }
}

#[derive(Debug, Clone, Copy, Hash, PartialEq, Eq)]
pub(crate) enum Term {
    Var(usize),
    App(Perfect<Application>),
}

impl fmt::Display for Term {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            Term::Var(x) => write!(f, "X{x}"),
            Term::App(app) => write!(f, "{app}"),
        }
    }
}

impl IsGround for Term {
    fn is_ground(&self) -> bool {
        match self {
            Term::Var(_) => false,
            Term::App(app) => app.ground,
        }
    }
}

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub(crate) struct Literal {
    pub(crate) polarity: bool,
    pub(crate) atom: Perfect<Application>,
}

impl fmt::Display for Literal {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        if self.atom.symbol.is_equality() {
            self.atom.args[0].fmt(f)?;
            write!(f, "{}", if self.polarity { " = " } else { " != " })?;
            self.atom.args[1].fmt(f)
        } else {
            if !self.polarity {
                write!(f, "~")?;
            }
            write!(f, "{}", self.atom)
        }
    }
}

impl IsGround for Literal {
    fn is_ground(&self) -> bool {
        self.atom.is_ground()
    }
}

#[derive(Clone, Copy, Debug, Hash, PartialEq, Eq)]
pub(crate) struct Disequation {
    pub(crate) left: Term,
    pub(crate) right: Term,
}

impl fmt::Display for Disequation {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{} ≠ {}", self.left, self.right)
    }
}

#[derive(Debug, Clone, Copy)]
pub(crate) struct Extension {
    pub(crate) clause: &'static Clause,
    pub(crate) index: usize,
}

#[derive(Debug, Clone)]
pub(crate) enum Source {
    Reflexivity,
    Symmetry,
    Transitivity,
    Congruence,
    Axiom {
        path: Arc<String>,
        name: Arc<String>,
        original: Option<Arc<String>>,
    },
}

#[derive(Debug, Clone)]
pub(crate) struct Info {
    pub(crate) negated: bool,
    pub(crate) is_goal: bool,
    pub(crate) number: usize,
    pub(crate) source: Source,
}

#[derive(Debug)]
pub(crate) struct Clause {
    pub(crate) literals: Vec<Literal>,
    pub(crate) disequations: Vec<Disequation>,
    pub(crate) info: Info,
}

impl fmt::Display for Clause {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(
            f,
            "cnf({}, {}, ",
            self.info.number,
            if self.info.is_goal {
                "negated_conjecture"
            } else {
                "axiom"
            }
        )?;
        if self.literals.is_empty() {
            write!(f, "$false")?;
        } else {
            write!(f, "{}", self.literals[0])?;
            for literal in &self.literals[1..] {
                write!(f, " | {literal}")?;
            }
        }
        write!(f, "). %")?;
        for disequation in &self.disequations {
            write!(f, " {disequation}")?;
        }
        Ok(())
    }
}

#[derive(Debug, Default)]
pub(crate) struct Matrix {
    pub(crate) clauses: Vec<&'static Clause>,
    pub(crate) start: Vec<&'static Clause>,
    pub(crate) index: FnvHashMap<(bool, Perfect<Symbol>), Vec<Extension>>,
    pub(crate) have_conjecture: bool,
}

#[derive(Debug, Clone, Hash, PartialEq, Eq, PartialOrd, Ord)]
pub(crate) enum RcTerm {
    Variable(usize),
    Application(Rc<RcApplication>),
}

impl RcTerm {
    fn variables(&self, vars: &mut FnvHashSet<usize>) {
        match self {
            Self::Variable(x) => {
                vars.insert(*x);
            }
            Self::Application(application) => {
                application.variables(vars);
            }
        }
    }
}

#[derive(Debug, Hash, PartialEq, Eq, PartialOrd, Ord)]
pub(crate) struct RcApplication {
    pub(crate) symbol: Perfect<Symbol>,
    pub(crate) args: Box<[RcTerm]>,
}

impl RcApplication {
    fn variables(&self, vars: &mut FnvHashSet<usize>) {
        for arg in &self.args {
            arg.variables(vars);
        }
    }
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub(crate) struct RcLiteral {
    pub(crate) polarity: bool,
    pub(crate) atom: Rc<RcApplication>,
}

impl RcLiteral {
    pub(crate) fn negated(&self) -> Self {
        Self {
            polarity: !self.polarity,
            atom: self.atom.clone(),
        }
    }

    pub(crate) fn variables(&self, vars: &mut FnvHashSet<usize>) {
        self.atom.variables(vars);
    }
}

#[derive(Debug)]
pub(crate) enum Formula {
    Bool(bool),
    Atom(Rc<RcApplication>),
    Not(Box<Formula>),
    And(Vec<Formula>),
    Or(Vec<Formula>),
    Iff(Box<Formula>, Box<Formula>),
    Forall(usize, Box<Formula>),
    Exists(usize, Box<Formula>),
}

impl Formula {
    pub(crate) fn negated(self) -> Self {
        Self::Not(Box::new(self))
    }
}

#[derive(Debug)]
pub(crate) enum Nnf {
    Literal(RcLiteral),
    And(Vec<Nnf>),
    Or(Vec<Nnf>),
    Forall(usize, Box<Nnf>),
    Exists(usize, Box<Nnf>),
}

impl Nnf {
    pub(crate) fn negated(&self) -> Self {
        match self {
            Self::Literal(lit) => Self::Literal(lit.negated()),
            Self::And(fs) => Self::Or(fs.iter().map(|f| f.negated()).collect()),
            Self::Or(fs) => Self::And(fs.iter().map(|f| f.negated()).collect()),
            Self::Forall(x, f) => Self::Exists(*x, Box::new(f.negated())),
            Self::Exists(x, f) => Self::Exists(*x, Box::new(f.negated())),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::rc::Rc;

    #[test]
    fn test_name_display() {
        println!("=== 测试名称显示 ===");

        let names = vec![
            Name::Equality,
            Name::Atom("predicate"),
            Name::Quoted("quoted_name"),
            Name::Number("42"),
            Name::Distinct("distinct_object"),
            Name::Skolem(1),
            Name::Definition(2),
        ];

        println!("各种名称类型的显示:");
        for name in &names {
            println!("  {:?} -> {}", name, name);
        }

        println!("\n合一需求检查:");
        for name in &names {
            println!("  {} 需要合一公理: {}", name, name.needs_congruence());
        }
    }

    #[test]
    fn test_symbol_creation() {
        println!("\n=== 测试符号创建 ===");

        // 创建各种类型的符号
        let symbols = vec![
            Symbol {
                arity: 0,
                sort: Sort::Obj,
                name: Name::Atom("a"),
            },
            Symbol {
                arity: 1,
                sort: Sort::Bool,
                name: Name::Atom("P"),
            },
            Symbol {
                arity: 2,
                sort: Sort::Obj,
                name: Name::Atom("f"),
            },
            Symbol {
                arity: 2,
                sort: Sort::Bool,
                name: Name::Equality,
            },
            Symbol {
                arity: 0,
                sort: Sort::Obj,
                name: Name::Skolem(0),
            },
        ];

        println!("符号信息:");
        for (i, symbol) in symbols.iter().enumerate() {
            println!("  符号{}: {:?}", i, symbol);
            println!("    元数: {}", symbol.arity);
            println!("    类型: {:?}", symbol.sort);
            println!("    名称: {}", symbol.name);
            println!("    是等式: {}", symbol.is_equality());
            println!();
        }
    }

    #[test]
    fn test_term_construction() {
        println!("\n=== 测试项构造 ===");

        // 创建符号
        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        let f_symbol = Perfect::new(Symbol {
            arity: 2,
            sort: Sort::Obj,
            name: Name::Atom("f"),
        });

        // 创建常量 a
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        // 创建变量 X0
        let x0 = Term::Var(0);

        // 创建复合项 f(a, X0)
        let fax0_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::App(a_app), x0].into(),
            ground: false,
        });

        let terms = vec![
            Term::Var(0),
            Term::Var(1),
            Term::App(a_app),
            Term::App(fax0_app),
        ];

        println!("项的构造和显示:");
        for (i, term) in terms.iter().enumerate() {
            println!("  项{}: {}", i, term);
            println!("    是基项: {}", term.is_ground());
            println!("    调试信息: {:?}", term);
            println!();
        }
    }

    #[test]
    fn test_literal_construction() {
        println!("\n=== 测试文字构造 ===");

        // 创建谓词符号 P(x)
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        // 创建等式符号
        let eq_symbol = Perfect::new(Symbol {
            arity: 2,
            sort: Sort::Bool,
            name: Name::Equality,
        });

        // 创建常量 a
        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        // 创建 P(a)
        let pa_app = Perfect::new(Application {
            symbol: p_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        // 创建 X0 = a
        let eq_app = Perfect::new(Application {
            symbol: eq_symbol,
            args: vec![Term::Var(0), Term::App(a_app)].into(),
            ground: false,
        });

        let literals = vec![
            Literal {
                polarity: true,
                atom: pa_app,
            },
            Literal {
                polarity: false,
                atom: pa_app,
            },
            Literal {
                polarity: true,
                atom: eq_app,
            },
            Literal {
                polarity: false,
                atom: eq_app,
            },
        ];

        println!("文字的构造和显示:");
        for (i, literal) in literals.iter().enumerate() {
            println!("  文字{}: {}", i, literal);
            println!("    极性: {}", literal.polarity);
            println!("    是基项: {}", literal.is_ground());
            println!("    调试信息: {:?}", literal);
            println!();
        }
    }

    #[test]
    fn test_clause_construction() {
        println!("\n=== 测试子句构造 ===");

        // 创建符号
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        let q_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("Q"),
        });

        let a_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Obj,
            name: Name::Atom("a"),
        });

        // 创建应用
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        let pa_app = Perfect::new(Application {
            symbol: p_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        let qa_app = Perfect::new(Application {
            symbol: q_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        // 创建文字
        let p_literal = Literal {
            polarity: true,
            atom: pa_app,
        };

        let not_q_literal = Literal {
            polarity: false,
            atom: qa_app,
        };

        // 创建不等式
        let diseq = Disequation {
            left: Term::Var(0),
            right: Term::Var(1),
        };

        // 创建子句: P(a) ∨ ¬Q(a)
        let clause = Clause {
            literals: vec![p_literal, not_q_literal],
            disequations: vec![diseq],
            info: Info {
                negated: false,
                is_goal: false,
                number: 1,
                source: Source::Axiom {
                    path: Arc::new("test.p".to_string()),
                    name: Arc::new("axiom1".to_string()),
                    original: Some(Arc::new("P(a) | ~Q(a)".to_string())),
                },
            },
        };

        println!("子句信息:");
        println!("  显示: {}", clause);
        println!("  文字数量: {}", clause.literals.len());
        println!("  不等式数量: {}", clause.disequations.len());
        println!("  是否为目标: {}", clause.info.is_goal);
        println!("  是否否定: {}", clause.info.negated);
        println!("  编号: {}", clause.info.number);
        println!("  来源: {:?}", clause.info.source);

        println!("\n文字详情:");
        for (i, literal) in clause.literals.iter().enumerate() {
            println!("  文字{}: {}", i, literal);
        }

        println!("\n不等式详情:");
        for (i, diseq) in clause.disequations.iter().enumerate() {
            println!("  不等式{}: {}", i, diseq);
        }
    }

    #[test]
    fn test_rc_structures() {
        println!("\n=== 测试引用计数结构 ===");

        // 创建符号
        let f_symbol = Perfect::new(Symbol {
            arity: 2,
            sort: Sort::Obj,
            name: Name::Atom("f"),
        });

        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        // 创建 RcTerm
        let x0 = RcTerm::Variable(0);
        let x1 = RcTerm::Variable(1);

        // 创建 f(X0, X1)
        let f_app = Rc::new(RcApplication {
            symbol: f_symbol,
            args: vec![x0, x1].into(),
        });

        let f_term = RcTerm::Application(f_app.clone());

        // 创建 P(f(X0, X1))
        let p_app = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![f_term].into(),
        });

        // 创建文字 P(f(X0, X1))
        let literal = RcLiteral {
            polarity: true,
            atom: p_app,
        };

        println!("引用计数结构:");
        println!("  文字: {:?}", literal);
        println!("  极性: {}", literal.polarity);

        // 测试否定
        let neg_literal = literal.negated();
        println!("  否定文字: {:?}", neg_literal);
        println!("  否定极性: {}", neg_literal.polarity);

        // 测试变量收集
        let mut vars = FnvHashSet::default();
        literal.variables(&mut vars);
        println!("  包含的变量: {:?}", vars);
    }

    #[test]
    fn test_formula_structures() {
        println!("\n=== 测试公式结构 ===");

        // 创建符号
        let p_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        let q_symbol = Perfect::new(Symbol {
            arity: 0,
            sort: Sort::Bool,
            name: Name::Atom("Q"),
        });

        // 创建原子公式
        let p_app = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![].into(),
        });

        let q_app = Rc::new(RcApplication {
            symbol: q_symbol,
            args: vec![].into(),
        });

        // 创建各种公式
        let formulas = vec![
            Formula::Bool(true),
            Formula::Bool(false),
            Formula::Atom(p_app.clone()),
            Formula::Not(Box::new(Formula::Atom(q_app.clone()))),
            Formula::And(vec![
                Formula::Atom(p_app.clone()),
                Formula::Atom(q_app.clone()),
            ]),
            Formula::Or(vec![
                Formula::Atom(p_app.clone()),
                Formula::Not(Box::new(Formula::Atom(q_app.clone()))),
            ]),
            Formula::Forall(0, Box::new(Formula::Atom(p_app.clone()))),
            Formula::Exists(1, Box::new(Formula::Atom(q_app.clone()))),
        ];

        println!("公式结构:");
        for (i, formula) in formulas.iter().enumerate() {
            println!("  公式{}: {:?}", i, formula);
        }

        // 测试否定
        println!("\n否定公式:");
        let p_formula = Formula::Atom(p_app);
        let neg_p = p_formula.negated();
        println!("  原公式: {:?}", Formula::Atom(q_app.clone()));
        println!("  否定后: {:?}", neg_p);
    }

    #[test]
    fn test_nnf_structures() {
        println!("\n=== 测试否定范式结构 ===");

        // 创建符号
        let p_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("P"),
        });

        let q_symbol = Perfect::new(Symbol {
            arity: 1,
            sort: Sort::Bool,
            name: Name::Atom("Q"),
        });

        // 创建应用
        let p_app = Rc::new(RcApplication {
            symbol: p_symbol,
            args: vec![RcTerm::Variable(0)].into(),
        });

        let q_app = Rc::new(RcApplication {
            symbol: q_symbol,
            args: vec![RcTerm::Variable(0)].into(),
        });

        // 创建文字
        let p_literal = RcLiteral {
            polarity: true,
            atom: p_app,
        };

        let q_literal = RcLiteral {
            polarity: true,
            atom: q_app,
        };

        // 创建 NNF 结构
        let nnf_formulas = vec![
            Nnf::Literal(p_literal.clone()),
            Nnf::Literal(q_literal.clone()),
            Nnf::And(vec![
                Nnf::Literal(p_literal.clone()),
                Nnf::Literal(q_literal.clone()),
            ]),
            Nnf::Or(vec![
                Nnf::Literal(p_literal.clone()),
                Nnf::Literal(q_literal.negated()),
            ]),
            Nnf::Forall(0, Box::new(Nnf::Literal(p_literal.clone()))),
            Nnf::Exists(1, Box::new(Nnf::Literal(q_literal.clone()))),
        ];

        println!("否定范式结构:");
        for (i, nnf) in nnf_formulas.iter().enumerate() {
            println!("  NNF{}: {:?}", i, nnf);
        }

        // 测试否定
        println!("\n否定 NNF 公式:");
        for (i, nnf) in nnf_formulas.iter().enumerate() {
            let negated = nnf.negated();
            println!("  原NNF{}: {:?}", i, nnf);
            println!("  否定后: {:?}", negated);
            println!();
        }
    }

    #[test]
    fn test_matrix_structure() {
        println!("\n=== 测试矩阵结构 ===");

        let mut matrix = Matrix::default();

        println!("初始矩阵:");
        println!("  子句数量: {}", matrix.clauses.len());
        println!("  起始子句数量: {}", matrix.start.len());
        println!("  索引大小: {}", matrix.index.len());
        println!("  有猜想: {}", matrix.have_conjecture);

        // 注意：由于 Matrix 包含 &'static Clause，我们无法轻易创建测试数据
        // 这通常由 Builder 来处理
        println!("\n矩阵结构说明:");
        println!("  - clauses: 存储所有子句的引用");
        println!("  - start: 存储起始子句(通常是目标的否定)");
        println!("  - index: 按(极性, 符号)索引的扩展映射");
        println!("  - have_conjecture: 是否包含猜想");
    }

    #[test]
    fn test_syntax_workflow() {
        println!("\n=== 语法结构工作流程演示 ===");

        println!(
            "
这个测试演示了语法结构的完整工作流程:

1. 符号(Symbol)创建
2. 应用(Application)构造
3. 项(Term)组合
4. 文字(Literal)形成
5. 子句(Clause)构建
6. 矩阵(Matrix)组织

语法结构的层次关系:
- Name: 符号的名称(原子、数字、Skolem等)
- Symbol: 带有元数和类型的符号
- Application: 符号应用到参数
- Term: 变量或应用
- Literal: 带极性的原子公式
- Clause: 文字的析取
- Matrix: 子句的集合

特殊结构:
- RcTerm/RcApplication/RcLiteral: 使用引用计数的版本
- Formula: 一般的逻辑公式
- Nnf: 否定范式
        "
        );

        println!("\n=== 步骤1: 创建基础符号 ===");
        let symbols = vec![
            (
                "常量a",
                Symbol {
                    arity: 0,
                    sort: Sort::Obj,
                    name: Name::Atom("a"),
                },
            ),
            (
                "函数f",
                Symbol {
                    arity: 2,
                    sort: Sort::Obj,
                    name: Name::Atom("f"),
                },
            ),
            (
                "谓词P",
                Symbol {
                    arity: 1,
                    sort: Sort::Bool,
                    name: Name::Atom("P"),
                },
            ),
            (
                "等式",
                Symbol {
                    arity: 2,
                    sort: Sort::Bool,
                    name: Name::Equality,
                },
            ),
        ];

        for (desc, symbol) in &symbols {
            println!(
                "  {}: {} (元数:{}, 类型:{:?})",
                desc, symbol.name, symbol.arity, symbol.sort
            );
        }

        println!("\n=== 步骤2: 构造项 ===");
        let a_symbol = Perfect::new(symbols[0].1.clone());
        let f_symbol = Perfect::new(symbols[1].1.clone());

        // 常量 a
        let a_app = Perfect::new(Application {
            symbol: a_symbol,
            args: vec![].into(),
            ground: true,
        });

        // 复合项 f(a, X0)
        let fax0_app = Perfect::new(Application {
            symbol: f_symbol,
            args: vec![Term::App(a_app), Term::Var(0)].into(),
            ground: false,
        });

        let terms = vec![
            ("变量X0", Term::Var(0)),
            ("常量a", Term::App(a_app)),
            ("复合项f(a,X0)", Term::App(fax0_app)),
        ];

        for (desc, term) in &terms {
            println!("  {}: {} (基项:{})", desc, term, term.is_ground());
        }

        println!("\n=== 步骤3: 构造文字 ===");
        let p_symbol = Perfect::new(symbols[2].1.clone());
        let eq_symbol = Perfect::new(symbols[3].1.clone());

        // P(a)
        let pa_app = Perfect::new(Application {
            symbol: p_symbol,
            args: vec![Term::App(a_app)].into(),
            ground: true,
        });

        // X0 = a
        let eq_app = Perfect::new(Application {
            symbol: eq_symbol,
            args: vec![Term::Var(0), Term::App(a_app)].into(),
            ground: false,
        });

        let literals = vec![
            (
                "正文字P(a)",
                Literal {
                    polarity: true,
                    atom: pa_app,
                },
            ),
            (
                "负文字¬P(a)",
                Literal {
                    polarity: false,
                    atom: pa_app,
                },
            ),
            (
                "等式X0=a",
                Literal {
                    polarity: true,
                    atom: eq_app,
                },
            ),
            (
                "不等式X0≠a",
                Literal {
                    polarity: false,
                    atom: eq_app,
                },
            ),
        ];

        for (desc, literal) in &literals {
            println!("  {}: {}", desc, literal);
        }

        println!("\n=== 工作流程总结 ===");
        println!("1. 从基础符号开始构建语法树");
        println!("2. 符号组合成应用，应用组合成项");
        println!("3. 项形成文字，文字组成子句");
        println!("4. 子句集合形成矩阵，用于证明搜索");
        println!("5. 特殊结构(Rc版本)用于内存效率和共享");
    }
}
