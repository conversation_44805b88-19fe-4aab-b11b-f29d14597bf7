# HopCop 连接表方法详解

## 概述

连接表(Tableau)方法是 HopCop 定理证明器的核心数据结构，用于系统地构建证明树。本文档通过具体例子详细说明连接表方法的工作原理和过程流程。

## 核心数据结构

### 1. 节点(Node)
连接表中的基本单元：

```rust
struct Node {
    branch: Branch,           // 来源分支
    depth: usize,            // 在树中的深度
    clause: &'static Clause, // 包含的子句
}
```

**作用**: 
- 存储子句在连接表中的位置信息
- 记录推理的层次结构
- 支持回溯和深度限制

### 2. 分支(Branch)
指向特定位置的特定文字：

```rust
struct Branch {
    location: Location,  // 连接表中的位置
    index: usize,       // 文字在子句中的索引
}
```

**表示**: `l0_0` 表示位置 l0 的第 0 个文字

### 3. 连接表(Tableau)
管理整个证明树结构：

```rust
struct Tableau {
    map: FnvHashMap<Branch, Location>,     // 分支到位置的映射
    nodes: IndexMap<Location, Node>,       // 位置到节点的映射
}
```

## 工作流程

### 阶段1: 初始化
```
1. 创建空连接表
2. 选择起始子句(通常是目标的否定)
3. 设置根节点
```

### 阶段2: 分支扩展
```
1. 选择开放分支
2. 寻找可以关闭该分支的子句
3. 添加新子句到连接表
4. 创建新的开放分支
```

### 阶段3: 连接检查
```
1. 扩展规则: 添加新子句
2. 归约规则: 与祖先节点统一
3. 统一检查: 验证文字可以统一
```

## 实际例子演示

### 例子1: 简单连接表构造

**问题**: 证明 `{P(a), ¬Q(a)}` 是不一致的

**步骤**:
```
1. 初始化: 空连接表
   大小: 0

2. 设置根子句: ¬Q(a)
   l0: ¬Q(a) (深度 0)
   大小: 1

3. 分析开放分支:
   分支 l0_0: ¬Q(a) 需要被关闭

4. 扩展(如果有合适的子句):
   需要找到包含 Q(a) 的子句
```

### 例子2: 复杂连接表构造

**问题**: 证明 `{P(a)∨Q(a), ¬P(a)∨R(a), ¬Q(a)∨R(a), ¬R(a)}` 是不一致的

**构造过程**:

#### 步骤1: 设置根子句
```
根子句: ¬R(a)
连接表:
  l0: ¬R(a) (深度 0)
开放分支: [l0_0]
```

#### 步骤2: 扩展根分支
```
扩展分支 l0_0 用子句: ¬Q(a) ∨ R(a)
连接表:
  l0: ¬R(a) (深度 0)
  l1: ¬Q(a) ∨ R(a) (深度 1, 来自 l0_0)
开放分支: [l1_0] (¬Q(a))
关闭分支: l0_0 (通过 l1_1 的 R(a))
```

#### 步骤3: 继续扩展
```
扩展分支 l1_0 用子句: P(a) ∨ Q(a)
连接表:
  l0: ¬R(a) (深度 0)
  l1: ¬Q(a) ∨ R(a) (深度 1)
  l2: P(a) ∨ Q(a) (深度 2, 来自 l1_0)
开放分支: [l2_0] (P(a))
关闭分支: l1_0 (通过 l2_1 的 Q(a))
```

#### 步骤4: 最后扩展
```
扩展分支 l2_0 用子句: ¬P(a) ∨ R(a)
连接表:
  l0: ¬R(a) (深度 0)
  l1: ¬Q(a) ∨ R(a) (深度 1)
  l2: P(a) ∨ Q(a) (深度 2)
  l3: ¬P(a) ∨ R(a) (深度 3, 来自 l2_0)
开放分支: [l3_1] (R(a))
关闭分支: l2_0 (通过 l3_0 的 ¬P(a))
```

#### 步骤5: 归约完成
```
分支 l3_1 的 R(a) 可以与祖先 l0_0 的 ¬R(a) 归约
所有分支都已关闭，证明完成！
```

## 关键操作

### 1. 设置根子句
```rust
tableau.set_root_clause(clause);
```
- 将子句设置为连接表的根节点
- 深度为 0
- 位置为 ROOT (l0)

### 2. 添加子句
```rust
tableau.add_clause(branch, clause);
```
- 为指定分支添加新子句
- 自动计算深度 (父节点深度 + 1)
- 分配新的位置标识

### 3. 位置映射
```rust
let location = tableau.locate(branch);
```
- 为分支分配唯一的位置标识
- 支持重复查询返回相同位置
- 用于建立父子关系

### 4. 结构查询
```rust
tableau.contains(location)  // 检查位置是否存在
tableau.len()              // 获取节点数量
tableau.is_empty()         // 检查是否为空
```

## Graphviz 可视化

连接表支持生成 Graphviz 格式的可视化：

```dot
digraph tableau {
    node [shape=none];
    l0_0 [label="~R(a)"];
    l1_0 [label="~Q(a)"];
    l1_1 [label="R(a)"];
    l0_0 -> l1_0;
    l0_0 -> l1_1;
}
```

**说明**:
- 每个节点表示一个文字
- 边表示父子关系
- 可以用 Graphviz 工具生成图形

## 测试用例

### 运行测试

```bash
# 基础连接表操作
cargo test test_tableau_basics -- --nocapture

# 分支位置映射
cargo test test_branch_location_mapping -- --nocapture

# 连接表构造过程
cargo test test_tableau_construction_process -- --nocapture

# 复杂连接表构造
cargo test test_complex_tableau_construction -- --nocapture

# 连接表操作测试
cargo test test_tableau_operations -- --nocapture

# Graphviz输出测试
cargo test test_graphviz_output -- --nocapture

# 完整工作流程
cargo test test_tableau_workflow -- --nocapture

# 运行所有连接表测试
cargo test tableau::tests -- --nocapture
```

## 实际输出示例

### 基础操作
```
初始状态:
  是否为空: true
  大小: 0

设置根子句后:
  大小: 1
  包含根位置: true
```

### 复杂构造
```
连接表结构分析:
  总节点数: 4
  深度分布:
    深度 0: 1 个节点
    深度 1: 1 个节点
    深度 2: 1 个节点
    深度 3: 1 个节点
```

### 节点详情
```
l0: 来自 l18446744073709551615_18446744073709551615 (深度 0)
  分支 l0_0: ~R(a)
l1: 来自 l0_0 (深度 1)
  分支 l1_0: ~Q(a)
  分支 l1_1: R(a)
```

## 在证明搜索中的作用

1. **结构化存储**: 以树状结构存储推理过程
2. **分支管理**: 跟踪开放和关闭的分支
3. **深度控制**: 支持深度限制的搜索
4. **回溯支持**: 支持搜索失败时的状态恢复
5. **可视化调试**: 提供图形化的证明过程展示

## 性能特性

1. **内存效率**: 使用 IndexMap 保持插入顺序
2. **快速查找**: HashMap 提供 O(1) 的分支查找
3. **增量操作**: 支持截断操作用于回溯
4. **位置复用**: 相同分支映射到相同位置

连接表方法是自动定理证明的经典技术，它提供了一种系统化、完备的证明搜索框架。通过这些测试用例，我们可以清楚地看到连接表如何构建和管理证明过程的树状结构。
